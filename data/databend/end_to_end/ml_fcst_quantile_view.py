from sqlalchemy import Column, String, Integer, DECIMAL, desc, Float

from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSessionV2
from util.conf import Base, logger
from util.util import env_dev


class MLFcstQuantileView(Base):
    __tablename__ = 'view_app_fast_ml_forecast_quantile_wi'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    session = SupplyDatabendSessionV2()
    if env_dev():
        __tablename__ = 'view_app_fast_ml_forecast_quantile_wi_test'
        __table_args__ = {'schema': 'test_db'}
        session = ComplianceDatabendSession()

    rtm = Column(String(10), comment='', primary_key=True)
    sub_rtm = Column(String(10), comment='')
    fiscal_week = Column(Integer, comment='')
    fiscal_week_name = Column(String(10), comment='')
    lob = Column(String(10), comment='')
    sub_lob = Column(String(10), comment='')
    quantile_level = Column(Float, comment='')
    ml_forecast_cw = Column(Integer, comment='')
    quantile_min_cw = Column(Integer, comment='')
    quantile_max_cw = Column(Integer, comment='')
    ml_forecast_cw1 = Column(Integer, comment='')
    quantile_min_cw1 = Column(Integer, comment='')
    quantile_max_cw1 = Column(Integer, comment='')
    ml_forecast_cw2 = Column(Integer, comment='')
    quantile_min_cw2 = Column(Integer, comment='')
    quantile_max_cw2 = Column(Integer, comment='')
    ml_forecast_cw3 = Column(Integer, comment='')
    quantile_min_cw3 = Column(Integer, comment='')
    quantile_max_cw3 = Column(Integer, comment='')
    ml_forecast_cw4 = Column(Integer, comment='')
    quantile_min_cw4 = Column(Integer, comment='')
    quantile_max_cw4 = Column(Integer, comment='')
    ml_forecast_cw5 = Column(Integer, comment='')
    quantile_min_cw5 = Column(Integer, comment='')
    quantile_max_cw5 = Column(Integer, comment='')
    ml_forecast_cw6 = Column(Integer, comment='')
    quantile_min_cw6 = Column(Integer, comment='')
    quantile_max_cw6 = Column(Integer, comment='')

    @classmethod
    def get_menu_data(cls, fiscal_week_name: str, lob: str, sub_lob: str = None):
        s = cls.session
        try:
            filter_params = [cls.fiscal_week_name == fiscal_week_name, cls.lob == lob]
            if sub_lob:
                filter_params.append(cls.sub_lob == sub_lob)
            res = (s.query(cls.fiscal_week_name, cls.fiscal_week, cls.sub_lob, cls.rtm, cls.sub_rtm)
                   .filter(*filter_params).distinct()
                   .order_by(desc(cls.fiscal_week)).all())
            return res
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_list_data(cls, fiscal_week_name: str, lob: str, sub_lob: str = None):
        s = cls.session
        try:
            filter_params = [cls.fiscal_week_name == fiscal_week_name, cls.lob == lob]
            if sub_lob:
                filter_params.append(cls.sub_lob == sub_lob)

            q = s.query(*cls.__table__.columns).filter(*filter_params)
            # print(f'>>>{q.statement.compile(compile_kwargs={"literal_binds": True}).string}')
            res = q.all()
            return res
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
