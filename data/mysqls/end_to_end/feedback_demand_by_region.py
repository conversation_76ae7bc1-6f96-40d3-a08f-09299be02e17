import traceback
from datetime import datetime
from typing import List, Any, Dict
import pandas as pd
from sqlalchemy import Column, Integer, String, Float

from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.mybusiness_base import MyBusinessBase, MyBusinessSession


class DemandFeedbackByRegion(MyBusinessBase):
    __tablename__ = 'feedback_demand_by_region'
    __table_args__ = {"schema": "mybusiness"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    fiscal_week = Column(Integer, comment='fiscal_week')
    region = Column(String(32), comment='region')
    lob = Column(String(32), comment='lob')
    sub_lob = Column(String(32), comment='sub_lob')
    nand = Column(String(32), comment='nand')
    color = Column(String(32), comment='color')
    mpn = Column(String(32), comment='mpn')
    hr_lr = Column(String(32), comment='hr_lr')
    shipment_plan_cw = Column(Float, comment='shipment_plan_cw')
    shipment_plan_cw1 = Column(Float, comment='shipment_plan_cw1')
    shipment_plan_cw2 = Column(Float, comment='shipment_plan_cw2')
    ub_eoh = Column(Integer, comment='ub_eoh')
    forecast_cw_ml = Column(Integer, comment='forecast_cw_ml')
    forecast_cw1_ml = Column(Integer, comment='forecast_cw1_ml')
    forecast_cw2_dfa = Column(Integer, comment='forecast_cw2_dfa')
    forecast_cw3_dfa = Column(Integer, comment='forecast_cw3_dfa')
    forecast_cw4_dfa = Column(Integer, comment='forecast_cw4_dfa')
    forecast_cw5_dfa = Column(Integer, comment='forecast_cw5_dfa')
    forecast_cw6_dfa = Column(Integer, comment='forecast_cw6_dfa')
    forecast_cw7_dfa = Column(Integer, comment='forecast_cw7_dfa')
    forecast_cw8_dfa = Column(Integer, comment='forecast_cw8_dfa')
    df_cw1 = Column(Float, comment='df_cw1')
    df_cw2 = Column(Float, comment='df_cw2')
    df_cw1_adjusted = Column(Float, comment='df_cw1_adjusted')
    df_cw2_adjusted = Column(Float, comment='df_cw2_adjusted')
    forecast_feedback_cw = Column(Integer, comment='forecast_feedback_cw')
    forecast_feedback_cw1 = Column(Integer, comment='forecast_feedback_cw1')
    forecast_feedback_cw2 = Column(Integer, comment='forecast_feedback_cw2')
    forecast_feedback_cw3 = Column(Integer, comment='forecast_feedback_cw3')
    forecast_feedback_cw4 = Column(Integer, comment='forecast_feedback_cw4')
    forecast_feedback_cw5 = Column(Integer, comment='forecast_feedback_cw5')
    forecast_feedback_cw6 = Column(Integer, comment='forecast_feedback_cw6')
    forecast_feedback_cw7 = Column(Integer, comment='forecast_feedback_cw7')
    forecast_feedback_cw8 = Column(Integer, comment='forecast_feedback_cw8')
    forecast_feedback_cw9 = Column(Integer, comment='forecast_feedback_cw9')
    forecast_feedback_cw10 = Column(Integer, comment='forecast_feedback_cw10')
    forecast_feedback_cw11 = Column(Integer, comment='forecast_feedback_cw11')
    forecast_feedback_cw12 = Column(Integer, comment='forecast_feedback_cw12')
    trial_demand_cw1 = Column(Float, comment='trial_demand_cw1')
    trial_demand_cw2 = Column(Float, comment='trial_demand_cw2')
    trial_po_needed_cw1 = Column(Float, comment='trial_po_needed_cw1')
    trial_po_needed_cw2 = Column(Float, comment='trial_po_needed_cw2')
    finalized_demand_v2_cw1 = Column(Float, comment='feedback_demand_cw1')
    finalized_demand_v2_cw2 = Column(Float, comment='feedback_demand_cw2')
    ori_finalized_demand_v2_cw1 = Column(Float, comment='ori_finalized_demand_v2_cw1')
    ori_finalized_demand_v2_cw2 = Column(Float, comment='ori_finalized_demand_v2_cw2')

    @classmethod
    def get_data_df(cls, fiscal_week_name: str, lob: str, sub_lobs: list[str], nand: list[str] = None, color: list[str] = None) -> pd.DataFrame:
        s = MyBusinessSession()
        try:
            filter_params = [cls.fiscal_week == fiscal_week_name, cls.lob == lob]
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if nand:
                filter_params.append(cls.nand.in_(nand))
            if color:
                filter_params.append(cls.color.in_(color))
            q = (s.query(cls)
                 .filter(*filter_params))
            return pd.read_sql(q.statement, s.bind)
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, '查询数据库失败')
        finally:
            s.close()

    @classmethod
    def batch_insert(cls, objs: list):
        s = MyBusinessSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now

            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, region, lob, sub_lob, nand, color, mpn, hr_lr, 
                 shipment_plan_cw, shipment_plan_cw1, shipment_plan_cw2, ub_eoh, 
                 forecast_cw_ml, forecast_cw1_ml, forecast_cw2_dfa, forecast_cw3_dfa, 
                 forecast_cw4_dfa, forecast_cw5_dfa, forecast_cw6_dfa, forecast_cw7_dfa, 
                 forecast_cw8_dfa, df_cw1, df_cw2, df_cw1_adjusted, df_cw2_adjusted, 
                 forecast_feedback_cw, forecast_feedback_cw1, forecast_feedback_cw2, 
                 forecast_feedback_cw3, forecast_feedback_cw4, forecast_feedback_cw5, 
                 forecast_feedback_cw6, forecast_feedback_cw7, forecast_feedback_cw8, 
                 forecast_feedback_cw9, forecast_feedback_cw10, forecast_feedback_cw11, 
                 forecast_feedback_cw12, trial_demand_cw1, trial_demand_cw2, 
                 trial_po_needed_cw1, trial_po_needed_cw2, finalized_demand_v2_cw1, 
                 finalized_demand_v2_cw2, create_time, update_time) 
                VALUES 
                (:fiscal_week, :region, :lob, :sub_lob, :nand, :color, :mpn, :hr_lr, 
                 :shipment_plan_cw, :shipment_plan_cw1, :shipment_plan_cw2, :ub_eoh, 
                 :forecast_cw_ml, :forecast_cw1_ml, :forecast_cw2_dfa, :forecast_cw3_dfa, 
                 :forecast_cw4_dfa, :forecast_cw5_dfa, :forecast_cw6_dfa, :forecast_cw7_dfa, 
                 :forecast_cw8_dfa, :df_cw1, :df_cw2, :df_cw1_adjusted, :df_cw2_adjusted, 
                 :forecast_feedback_cw, :forecast_feedback_cw1, :forecast_feedback_cw2, 
                 :forecast_feedback_cw3, :forecast_feedback_cw4, :forecast_feedback_cw5, 
                 :forecast_feedback_cw6, :forecast_feedback_cw7, :forecast_feedback_cw8, 
                 :forecast_feedback_cw9, :forecast_feedback_cw10, :forecast_feedback_cw11, 
                 :forecast_feedback_cw12, :trial_demand_cw1, :trial_demand_cw2, 
                 :trial_po_needed_cw1, :trial_po_needed_cw2, :finalized_demand_v2_cw1, 
                 :finalized_demand_v2_cw2, :create_time, :update_time)
                ON DUPLICATE KEY UPDATE 
                 finalized_demand_v2_cw1 = VALUES(finalized_demand_v2_cw1), 
                 finalized_demand_v2_cw2 = VALUES(finalized_demand_v2_cw2), 
                 update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def batch_update(cls, objs: List[Dict[str, Any]], fields_to_insert: List[str], fields_to_update: List[str]):
        s = MyBusinessSession()
        try:
            insert_parts = [f"{field}" for field in fields_to_insert]
            value_parts = [f":{field}" for field in fields_to_insert]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                                    INSERT INTO {cls.__tablename__} 
                                    (fiscal_week, mpn,  {', '.join(insert_parts)})
                                     VALUES 
                                        (:fiscal_week, :mpn,  {', '.join(value_parts)})
                                    ON DUPLICATE KEY UPDATE 
                                         {', '.join(key_parts)}
                                """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            s.rollback()
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, '更新数据库失败')
        finally:
            s.close()
