import traceback
from datetime import datetime
from typing import List, Dict, Any

import pandas as pd
from sqlalchemy import Column, Integer, String, Float, PrimaryKeyConstraint, DateTime

from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.mybusiness import MyBusinessBase, MyBusinessSession


class DemandFeedbackBySoldToView(MyBusinessBase):
    __tablename__ = 'feedback_demand_by_soldto_view'
    __table_args__ = (
        PrimaryKeyConstraint('fiscal_qtr_week_name', 'sold_to_id', 'mpn_id'),  # 定义组合主键
        {"schema": "mybusiness"}
    )

    id = Column(Integer, comment='ID')
    fiscal_week_year = Column(Integer, comment='fiscal_week_year')
    fiscal_qtr_week_name = Column(String(32), comment='fiscal_qtr_week_name')
    reseller_id = Column(Integer, comment='reseller_id')
    reseller_name = Column(String(256), comment='reseller_name')
    reseller_tier = Column(String(32), comment='reseller_tier')
    sold_to_id = Column(Integer, comment='sold_to_id')
    sold_to_name = Column(String(256), comment='sold_to_name')
    rtm = Column(String(32), comment='rtm')
    sub_rtm = Column(String(32), comment='sub_rtm')
    lob = Column(String(32), comment='lob')
    sub_lob = Column(String(32), comment='sub_lob')
    mpn_id = Column(String(32), comment='mpn_id')
    mpn_type = Column(String(32), comment='mpn_type')
    mpn_desc = Column(String(256), comment='mpn_desc')
    mpn_order = Column(Integer, comment='mpn_order')
    nand = Column(String(32), comment='nand')
    color = Column(String(32), comment='color')
    forecast_feedback_cw = Column(Integer, comment='forecast_feedback_cw')
    forecast_feedback_cw1 = Column(Integer, comment='forecast_feedback_cw1')
    forecast_feedback_cw2 = Column(Integer, comment='forecast_feedback_cw2')
    forecast_feedback_cw3 = Column(Integer, comment='forecast_feedback_cw3')
    forecast_feedback_cw4 = Column(Integer, comment='forecast_feedback_cw4')
    forecast_feedback_cw5 = Column(Integer, comment='forecast_feedback_cw5')
    forecast_feedback_cw6 = Column(Integer, comment='forecast_feedback_cw6')
    forecast_feedback_cw7 = Column(Integer, comment='forecast_feedback_cw7')
    forecast_feedback_cw8 = Column(Integer, comment='forecast_feedback_cw8')
    forecast_feedback_cw9 = Column(Integer, comment='forecast_feedback_cw9')
    forecast_feedback_cw10 = Column(Integer, comment='forecast_feedback_cw10')
    forecast_feedback_cw11 = Column(Integer, comment='forecast_feedback_cw11')
    forecast_feedback_cw12 = Column(Integer, comment='forecast_feedback_cw12')
    trial_demand_cw1 = Column(Float, comment='trial_demand_cw1')
    trial_demand_cw2 = Column(Float, comment='trial_demand_cw2')
    trial_po_needed_cw1 = Column(Float, comment='trial_po_needed_cw1')
    trial_po_needed_cw2 = Column(Float, comment='trial_po_needed_cw2')
    finalized_demand_v2_cw1 = Column(Float, comment='finalized_demand_v2_cw1')
    finalized_demand_v2_cw2 = Column(Float, comment='finalized_demand_v2_cw2')
    forecast_cw = Column(Integer, comment='forecast_cw')
    forecast_cw1 = Column(Integer, comment='forecast_cw1')
    forecast_cw2 = Column(Integer, comment='forecast_cw2')
    forecast_cw3 = Column(Integer, comment='forecast_cw3')
    forecast_cw4 = Column(Integer, comment='forecast_cw4')
    forecast_cw5 = Column(Integer, comment='forecast_cw5')
    forecast_cw6 = Column(Integer, comment='forecast_cw6')
    forecast_cw7 = Column(Integer, comment='forecast_cw7')
    forecast_cw8 = Column(Integer, comment='forecast_cw8')
    forecast_cw9 = Column(Integer, comment='forecast_cw9')
    forecast_cw10 = Column(Integer, comment='forecast_cw10')
    forecast_cw11 = Column(Integer, comment='forecast_cw11')
    forecast_cw12 = Column(Integer, comment='forecast_cw12')
    shipment_plan_cw = Column(Float, comment='shipment_plan_cw')
    shipment_plan_cw1 = Column(Float, comment='shipment_plan_cw1')
    shipment_plan_cw2 = Column(Float, comment='shipment_plan_cw2')
    open_backlog_over_published_sp = Column(Integer, comment='open_backlog_over_published_sp')
    ub_eoh_lw = Column(Integer, comment='ub_eoh_lw')
    final_demand_cw1 = Column(Float, comment='final_demand_cw1')
    final_demand_cw2 = Column(Float, comment='final_demand_cw2')
    po_needed_cw1 = Column(Float, comment='po_needed_cw1')
    po_needed_cw2 = Column(Float, comment='po_needed_cw2')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    publish_status = Column(Integer)
    publish_time = Column(DateTime)

    @classmethod
    def get_data_df(cls, fiscal_week_name: str, lob: str = 'iPhone', sub_lobs: list[str] = None, nand: list[str] = None,
                    color: list[str] = None, rtm: str = None, published_status: int = None) -> pd.DataFrame:
        s = MyBusinessSession()
        try:
            filter_params = [cls.fiscal_qtr_week_name == fiscal_week_name, cls.lob == lob]
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if nand:
                filter_params.append(cls.nand.in_(nand))
            if color:
                filter_params.append(cls.color.in_(color))
            if rtm:
                filter_params.append(cls.rtm == rtm)
            if published_status is not None:
                filter_params.append(cls.publish_status == published_status)
            q = (s.query(cls)
                 .filter(*filter_params))
            return pd.read_sql(q.statement, s.bind)
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, '查询数据库失败')
        finally:
            s.close()

    @classmethod
    def batch_insert(cls, objs: list):
        s = MyBusinessSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now

            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week_year, fiscal_qtr_week_name, reseller_id, reseller_name, reseller_tier, 
                 sold_to_id, sold_to_name, rtm, sub_rtm, lob, sub_lob ,nand, color, mpn_id, mpn_type, mpn_desc, mpn_order,
                 forecast_feedback_cw, forecast_feedback_cw1, forecast_feedback_cw2, forecast_feedback_cw3, 
                 forecast_feedback_cw4, forecast_feedback_cw5, forecast_feedback_cw6, forecast_feedback_cw7, 
                 forecast_feedback_cw8, forecast_feedback_cw9, forecast_feedback_cw10, forecast_feedback_cw11, 
                 forecast_feedback_cw12, trial_demand_cw1, trial_demand_cw2, trial_po_needed_cw1, 
                 trial_po_needed_cw2, finalized_demand_v2_cw1, finalized_demand_v2_cw2, forecast_cw, forecast_cw1, 
                 forecast_cw2, forecast_cw3, forecast_cw4, forecast_cw5, forecast_cw6, forecast_cw7, 
                 forecast_cw8, forecast_cw9, forecast_cw10, forecast_cw11, forecast_cw12, shipment_plan_cw, 
                 shipment_plan_cw1, shipment_plan_cw2, ub_eoh_lw, final_demand_cw1, final_demand_cw2, 
                 po_needed_cw1, po_needed_cw2, open_backlog_over_published_sp, create_time, update_time)
                VALUES 
                (:fiscal_week_year, :fiscal_qtr_week_name, :reseller_id, :reseller_name, :reseller_tier, 
                 :sold_to_id, :sold_to_name, :rtm, :sub_rtm, :lob, :sub_lob,:nand,:color, :mpn_id, :mpn_type, :mpn_desc, :mpn_order,
                 :forecast_feedback_cw, :forecast_feedback_cw1, :forecast_feedback_cw2, :forecast_feedback_cw3, 
                 :forecast_feedback_cw4, :forecast_feedback_cw5, :forecast_feedback_cw6, :forecast_feedback_cw7, 
                 :forecast_feedback_cw8, :forecast_feedback_cw9, :forecast_feedback_cw10, :forecast_feedback_cw11, 
                 :forecast_feedback_cw12, :trial_demand_cw1, :trial_demand_cw2, :trial_po_needed_cw1, 
                 :trial_po_needed_cw2, :finalized_demand_v2_cw1, :finalized_demand_v2_cw2, :forecast_cw, :forecast_cw1, 
                 :forecast_cw2, :forecast_cw3, :forecast_cw4, :forecast_cw5, :forecast_cw6, :forecast_cw7, 
                 :forecast_cw8, :forecast_cw9, :forecast_cw10, :forecast_cw11, :forecast_cw12, :shipment_plan_cw, 
                 :shipment_plan_cw1, :shipment_plan_cw2, :ub_eoh_lw, :final_demand_cw1, :final_demand_cw2, 
                 :po_needed_cw1, :po_needed_cw2, :open_backlog_over_published_sp, :create_time, :update_time)
                ON DUPLICATE KEY UPDATE 
                 finalized_demand_v2_cw1 = VALUES(finalized_demand_v2_cw1), 
                 finalized_demand_v2_cw2 = VALUES(finalized_demand_v2_cw2), 
                 update_time = VALUES(update_time)    
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def batch_update(cls, objs: List[Dict[str, Any]], fields_to_insert: List[str], fields_to_update: List[str]):
        s = MyBusinessSession()
        try:
            insert_parts = [f"{field}" for field in fields_to_insert]
            value_parts = [f":{field}" for field in fields_to_insert]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                                    INSERT INTO {cls.__tablename__} 
                                    (fiscal_qtr_week_name, sold_to_id, mpn_id,  {', '.join(insert_parts)})
                                     VALUES 
                                        (:fiscal_qtr_week_name, :sold_to_id, :mpn_id,  {', '.join(value_parts)})
                                    ON DUPLICATE KEY UPDATE 
                                         {', '.join(key_parts)}
                                """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            s.rollback()
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, '更新数据库失败')
        finally:
            s.close()

    @classmethod
    def find_by_fiscal_week_name(cls, fiscal_week_name: str) -> list[dict]:
        s = MyBusinessSession()
        try:
            query_result = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_week_name).all()
            if not query_result:
                return []
            return [record.__dict__ for record in query_result]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_publish_status(cls, fiscal_week_name: str, lob: str) -> bool:
        s = MyBusinessSession()
        try:
            query_result = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_week_name, cls.lob == lob,
                                               cls.publish_status == 1).limit(1).all()
            if not query_result:
                return False
            return True
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()