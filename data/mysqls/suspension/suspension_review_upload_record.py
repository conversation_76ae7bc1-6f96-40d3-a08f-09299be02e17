from util.fast_lite_base import FASTLiteBase
from util.mono_allocation_base import *


class SuspensionReviewUploadRecord(FASTLiteBase):
    __tablename__ = "suspension_review_upload_record"
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    sub_rtm = Column(String(32), default=None)
    t1_disti_id = Column(String(255), default=None)
    t1_disti_name = Column(String(255), default=None)
    t2_hq_id = Column(String(255), default=None)
    t2_reseller_name = Column(String(255), default=None)
    pos_id = Column(String(255), default=None)
    pos_name = Column(String(255), default=None)
    store_type = Column(String(255), default=None)
    is_active = Column(String(255), default=None)
    lob = Column(String(255), default=None)
    sub_lob = Column(String(255), default=None)
    so_fiscal_week = Column(String(255), default=None)
    so_date = Column(String(255), default=None)
    client_ip_address = Column(String(255), default=None)
    sn = Column(String(255), default=None)
    random_id = Column(String(255), default=None)
    so_province = Column(String(255), default=None)
    so_city = Column(String(255), default=None)
    ub_city = Column(String(255), default=None)
    baidu_cross_check_result = Column(String(255), default=None)
    tencent_cross_check_result = Column(String(255), default=None)
    dmp_judgment = Column(String(255), default=None)
    compliance_cross_check = Column(String(255), default=None)
    compliance_judge = Column(String(255), default=None)
    dmp_adjust_data_in_sandbox = Column(String(255), default=None)
    check_date = Column(String(255), default=None)
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def batch_insert(cls, objs: list, db_session: Session, now: datetime):
        try:
            objs = [obj.to_dict() for obj in objs]
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                    INSERT INTO {cls.__tablename__} 
                    (sub_rtm, t1_disti_id, t1_disti_name, t2_hq_id, t2_reseller_name, pos_id, pos_name, store_type, 
                    is_active, lob,sub_lob, so_fiscal_week, so_date, client_ip_address, sn, random_id, so_province, 
                    so_city, ub_city, baidu_cross_check_result, tencent_cross_check_result, dmp_judgment, 
                    compliance_cross_check, compliance_judge, dmp_adjust_data_in_sandbox,check_date, create_time, update_time) 
                     VALUES 
                        (:sub_rtm, :t1_disti_id, :t1_disti_name, :t2_hq_id,:t2_reseller_name,:pos_id,:pos_name,
                        :store_type,:is_active,:lob,:sub_lob,:so_fiscal_week,:so_date,:client_ip_address,:sn,:random_id,
                        :so_province,:so_city,:ub_city,:baidu_cross_check_result,:tencent_cross_check_result,
                        :dmp_judgment,:compliance_cross_check,:compliance_judge,:dmp_adjust_data_in_sandbox,:check_date, 
                        :create_time, :update_time)
                """
            db_session.execute(insert_stmt, objs)
        except Exception as e:
            logger.exception(e)
            raise e
        return True
