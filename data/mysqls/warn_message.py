from sqlalchemy import *
from datetime import datetime

from util.conf import logger
from util.fast_lite_base import FASTLiteBase, FASTLiteSession

CLIENT_TYPE_PC = 'PC'
WARN_PAGE_TYPE = "Smart Tower Inventory"


class WarnMessage(FASTLiteBase):
    __tablename__ = 'tbl_tower_warn_message'
    __table_args__ = { 'schema': 'Dashboard' }

    id = Column(Integer, primary_key=True, autoincrement=True,
                nullable=False, comment='ID')
    page_type = Column(String(64), comment='page type')
    client_type = Column(String(64), comment='client type')
    start_date = Column(DateTime, default=datetime.now, comment='start date')
    end_date = Column(DateTime, default=datetime.now, comment='start date')
    description = Column(Text, comment='description')
    order = Column(Integer, default=0, comment='order')
    deleted = Column(Integer, default=0, comment='0:exist 1:deleted')
    create_person = Column(String(32), comment='creater')
    create_person_name = Column(String(64), comment='creater name')
    create_date = Column(
        DateTime, default=datetime.now, comment='create time')

    @classmethod
    def get_page_message(cls,
                         page_type: str = WARN_PAGE_TYPE,
                         client_type: str = CLIENT_TYPE_PC) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            query_date = datetime.now()
            q = (s.query(cls.description)
                 .filter(cls.deleted == 0)
                 .filter(cls.page_type == page_type)
                 .filter(cls.client_type.like("%{}%".format(client_type)))
                 .filter(cls.start_date <= query_date)
                 .filter(cls.end_date >= query_date)
                )
            result = q.order_by(cls.order.asc()).all()
            ret = [item[0] for item in result]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret