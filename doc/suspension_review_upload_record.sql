CREATE TABLE IF NOT EXISTS fast_lite.suspension_review_upload_record (
  `id` int NOT NULL AUTO_INCREMENT,
  `sub_rtm` varchar(32)  DEFAULT NULL,
  `t1_disti_id` varchar(64) DEFAULT NULL,
  `t1_disti_name` varchar(255) DEFAULT NULL,
  `t2_hq_id` varchar(64) DEFAULT NULL,
  `t2_reseller_name` varchar(255) DEFAULT NULL,
  `pos_id` varchar(64) DEFAULT NULL,
  `pos_name` varchar(255) DEFAULT NULL,
  `store_type` varchar(255) DEFAULT NULL,
  `is_active` varchar(255) DEFAULT NULL,
  `lob` varchar(32) DEFAULT NULL,
  `sub_lob` varchar(64) DEFAULT NULL,
  `so_fiscal_week` varchar(64) DEFAULT NULL,
  `so_date` varchar(64) DEFAULT NULL,
  `client_ip_address` varchar(255) DEFAULT NULL,
  `sn` varchar(255) DEFAULT NULL,
  `random_id`varchar(255) DEFAULT NULL,
  `so_province` varchar(64) DEFAULT NULL,
  `so_city` varchar(64) DEFAULT NULL,
  `ub_city` varchar(64) DEFAULT NULL,
  `baidu_cross_check_result` varchar(255) DEFAULT NULL,
  `tencent_cross_check_result` varchar(255) DEFAULT NULL,
  `dmp_judgment` varchar(255) DEFAULT NULL,
  `compliance_cross_check` varchar(255) DEFAULT NULL,
  `compliance_judge` varchar(255) DEFAULT NULL,
  `dmp_adjust_data_in_sandbox` varchar(255) DEFAULT NULL,
  `check_date` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
