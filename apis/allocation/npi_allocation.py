import traceback
from typing import List

from apis.api_common import *
from data.mysqls.npi_allocation.allocation_result_dao import AllocationResultDao
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationSupplyConfigDto
from domain.npi_allocation.impl.npi_allocation_impl import allocation_supply_config, save_allocation_supply_config, \
    get_sold_to_mapping, get_dashboard, delete_allocation_result, get_pos_allocation_mix_file, \
    get_cdc_allocation_mix_file, save_china_total_config_by_max_version
from domain.npi_allocation.npi_allocation_week7 import NpiAllocationWeek7ByPos
from kit.tree_builder import TreeBuilder

bp = Blueprint("npi_allocation", __name__, url_prefix="/npi_allocation")


@bp.route("/save_supply_config", methods=["POST"])
def save_allocation_supply_configs():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        json_data = request.get_json()
        configs: List[AllocationSupplyConfigDto] = [
            AllocationSupplyConfigDto.from_dict(item) for item in json_data
        ]
        calculate_vertical(configs)
        version = save_allocation_supply_config(configs)
        try:
            NpiAllocationWeek7ByPos(supply_config=configs, version=version).allocation()
        except Exception as e:
            logger.error(traceback.format_exc())
            raise e
        delete_allocation_result()
        ret[Ret.Data] = version
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/save_china_total", methods=["POST"])
def save_china_total_config():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        tier = request.args.get('tier')
        total_china_supply = request.args.get('total_china_supply')
        npi_weekly_so_forecast = request.args.get('npi_weekly_so_forecast')
        woi_cap_adjustment = request.args.get('woi_cap_adjustment')
        save_china_total_config_by_max_version(tier=tier,
                                               total_china_supply=total_china_supply,
                                               npi_weekly_so_forecast=npi_weekly_so_forecast,
                                               woi_cap_adjustment=woi_cap_adjustment)
        ret[Ret.Data] = 'SUCCESS'
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/save_and_view", methods=["POST"])
def save_and_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        json_data = request.get_json()
        configs: List[AllocationSupplyConfigDto] = [
            AllocationSupplyConfigDto.from_dict(item) for item in json_data
        ]
        calculate_vertical(configs)
        version = save_allocation_supply_config(configs)
        try:
            NpiAllocationWeek7ByPos(supply_config=configs, version=version).allocation()
        except Exception as e:
            logger.error(traceback.format_exc())
            raise e
        delete_allocation_result()
        tier = request.args.get('tier')
        rtm = request.args.get('rtm')
        sub_rtm = request.args.get('sub_rtm')
        sold_to_id = request.args.get('sold_to_id')
        menu_result, dashboard = get_dashboard(tier, rtm, sub_rtm, sold_to_id)
        roots = TreeBuilder.build_tree(dashboard, id_getter=lambda x: x.id,
                                       parent_id_getter=lambda x: x.parent_id)
        ret[Ret.Data] = {
            "menu": menu_result,
            "list": roots
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/save", methods=["POST"])
def save():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        json_data = request.get_json()
        configs: List[AllocationSupplyConfigDto] = [
            AllocationSupplyConfigDto.from_dict(item) for item in json_data
        ]
        calculate_vertical(configs)
        version = save_allocation_supply_config(configs)
        ret[Ret.Data] = version
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


def calculate_vertical(configs):
    pro_ori_total_rtm_mix = 0
    pro_vertical_total_config = None
    consumer_ori_total_rtm_mix = 0
    consumer_vertical_total_config = None
    for config in configs:
        if config.supply_from == 'Vertical' and config.tier == 'Pro' and config.soldto_id != 'Total':
            pro_ori_total_rtm_mix += config.rtm_mix
        if config.supply_from == 'Vertical' and config.tier == 'Pro' and config.soldto_id == 'Total':
            pro_vertical_total_config = config
        if config.supply_from == 'Vertical' and config.tier == 'Consumer' and config.soldto_id != 'Total':
            consumer_ori_total_rtm_mix += config.rtm_mix
        if config.supply_from == 'Vertical' and config.tier == 'Consumer' and config.soldto_id == 'Total':
            consumer_vertical_total_config = config
    # 只处理supply
    for config in configs:
        if config.supply_from == 'Vertical' and config.tier == 'Pro' and config.soldto_id != 'Total':
            config.supply_qty = pro_vertical_total_config.supply_qty * (config.rtm_mix/pro_ori_total_rtm_mix)
        if config.supply_from == 'Vertical' and config.tier == 'Consumer' and config.soldto_id != 'Total':
            config.supply_qty = consumer_vertical_total_config.supply_qty * (config.rtm_mix/consumer_ori_total_rtm_mix)


@bp.route("/supply_config", methods=["GET"])
def get_allocation_supply_configs():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success", "data": []}
    try:
        ret[Ret.Data] = {
            "supply_config": allocation_supply_config(),
            "sold_to_list": get_sold_to_mapping()
        }
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/publish", methods=["GET"])
def publish():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success", "data": []}
    try:
        AllocationResultDao().update_publish_status_for_latest_versions()
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/publish_status", methods=["GET"])
def publish_status():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success", "data": []}
    try:
        ret[Ret.Data] = AllocationResultDao().has_published_records()
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/view", methods=["GET"])
def view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success", "data": []}
    try:
        tier = request.args.get('tier')
        rtm = request.args.get('rtm')
        sub_rtm = request.args.get('sub_rtm')
        sold_to_id = request.args.get('sold_to_id')
        # version = request.args.get('version')
        menu_result, dashboard = get_dashboard(tier, rtm, sub_rtm, sold_to_id)
        roots = TreeBuilder.build_tree(dashboard, id_getter=lambda x: x.id,
                                       parent_id_getter=lambda x: x.parent_id)
        ret[Ret.Data] = {
            "menu": menu_result,
            "list": roots
        }
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/pos_allocation_mix/download", methods=["GET"])
def download_pos_allocation_mix():
    rtm = request.args.get('rtm')
    rtm = __get_rtm(rtm)
    file_bytes, file_name = get_pos_allocation_mix_file(rtm)
    response = make_response(
        send_file(
            file_bytes,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            download_name=file_name,
            as_attachment=True,
        )
    )
    response.headers["Content-Disposition"] = f'attachment; filename={file_name}'
    return response


def __get_rtm(rtm):
    rtm = '' if rtm is None or rtm == 'CP&F' else rtm
    rtm = 'Enterprise' if rtm == 'ENT' else rtm
    rtm = 'Education' if rtm == 'EDU' else rtm
    return rtm


@bp.route("/cdc_allocation_mix/download", methods=["GET"])
def download_cdc_allocation_mix():
    rtm = request.args.get('rtm')
    file_bytes, file_name = get_cdc_allocation_mix_file(rtm)
    response = make_response(
        send_file(
            file_bytes,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            download_name=file_name,
            as_attachment=True,
        )
    )
    response.headers["Content-Disposition"] = f'attachment; filename={file_name}'
    return response
