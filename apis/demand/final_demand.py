import traceback


from apis.api_common import *
from data.mysqls.demand.weekly_demand_setting import WeeklyDemandSetting
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.demand.entity.const import FINAL_DEMAND, RTMS_NO_RETAIL
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.calculator.calculator_factory import calculator_factory
from domain.demand.impl.data_configuration_resolver import DataConfigurationResolver

from domain.demand.impl.final_demand_resolver import FinalDemandResolver, \
    publish, upload, get_sub_lob_publish_detail, upload_v3
from domain.demand.impl.ideal_demand_result import FinalDemandResult
from domain.demand.impl.state_machine import StateProxy
from domain.permission.impl.permission_impl import fast_e2e_permissions
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.custom_sort import CustomSort

bp = Blueprint('final_demand', __name__, url_prefix=UrlPrefixdemand)


@bp.route('/final_demand/overview_sublobs', methods=["GET"])
def query_final_demand_overview():
    fiscal_week = request.args.get('fiscal_week')
    channel = request.args.get('channel', None, str)
    if not fiscal_week or not channel:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    sub_lob_publish_details = get_sub_lob_publish_detail(fiscal_week)
    sub_lob_list = [item.sub_lob for item in sub_lob_publish_details]
    sub_lob_list = CustomSort.sort_iphone_sublobs(sub_lob_list)
    proxy = StateProxy(fiscal_week, FINAL_DEMAND)
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {
        'lob': "iPhone",
        "status": proxy.current_state().format(),
        "menu": {
            "sub_lobs": sub_lob_list,
            "rtms": RTMS_NO_RETAIL
        }
    }}
    rtm_list = RTMS_NO_RETAIL
    if channel != 'CP&F':
        rtm_list = [channel]
    try:
        publish_details = get_sub_lob_publish_detail(fiscal_week)
        # 将publish_details按照sub_lob_list的顺序排序
        if sub_lob_list and publish_details:
            publish_details = sorted(
                publish_details,
                key=lambda x: sub_lob_list.index(x.sub_lob) if x.sub_lob in sub_lob_list else float('inf')
            )
        ret[Ret.Data]["overview"] = [detail.to_dict() for detail in publish_details]
        # ret[Ret.Data]["overview"] = overview(fiscal_week, rtm_list)
    except Exception as e:
        logger.error(traceback.format_exc())
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/final_demand/publish', methods=["GET"])
def publish_final_demand():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    fiscal_week = request.args.get('fiscal_week')
    sub_lob = request.args.get('sub_lob')
    if not fiscal_week or not sub_lob:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))
    try:
        # 操作人信息
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        logger.info(
            f"final demand-->publish: fiscal_week:{fiscal_week} sub_lob:{sub_lob}, operator:{operator}")
        result = publish(fiscal_week, sub_lob, operator)
        logger.info(
            f"final demand-->publish: fiscal_week:{fiscal_week} sub_lob:{sub_lob}, operator:{operator} publish success")
        ret[Ret.Data] = result
    except Exception as e:
        logger.error(traceback.format_exc())
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/final_demand/detail', methods=["GET"])
def query_final_demand_detail():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week')
    rtms = request.args.get('rtms')
    if not rtms:  # 前端组件reset时会传空
        rtms = 'All'
    sub_lob = request.args.get('sub_lob')
    forecast_version = request.args.get('forecast_version')

    if not fiscal_week or not sub_lob or not forecast_version:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))
    try:
        rtm_list = rtms.split(',')
        rtm_list = RTMS_NO_RETAIL if 'All' in rtm_list else rtm_list
        resolver = FinalDemandResolver(fiscal_week=fiscal_week,
                                       sub_lobs=[sub_lob], rtms=rtm_list,
                                       forecast_version=forecast_version)
        details = resolver.query_final_demand_detail(forecast_version)
        ret[Ret.Data] = [detail.to_dict() for detail in details]
    except Exception as e:
        logger.error(traceback.format_exc())
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/final_demand/download', methods=["GET"])
@fast_e2e_permissions(request)
def download_final_demand_detail():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    channel = request.args.get('channel', None, str)
    sub_lobs = request.args.get('sub_lobs')

    try:
        # 必填校验
        if not fiscal_week or not channel or not sub_lobs:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        sub_lob_list = sub_lobs.split(',')

        delta_demand_result = FinalDemandResult(fiscal_week, FINAL_DEMAND, 0, sub_lob_list, channel)
        # delta_demand_result = SellInDemandResult(fiscal_week, SELL_IN_DEMAND, category)
        excel_file = delta_demand_result.download()

        file_name = delta_demand_result.file_info()["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/final_demand/upload", methods=["POST"])
def upload_final_demand():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        params = request.args
        fiscal_week = params.get("fiscal_week", "", str)
        file = request.files.get('file')
        if not fiscal_week or file is None:
            raise ErrorExcept(ErrCode.Param, "Param is required, please double check.")

        # 操作人信息
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        logger.info(
            f"final demand-->upload ready: fiscal_week:{fiscal_week}, uploader:{operator}")

        ret[Ret.Data] = upload(fiscal_week, file, operator)
        logger.info(
            f"final demand-->upload success: fiscal_week:{fiscal_week}, uploader:{operator}")
        return ret

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route('/final_demand/calculate', methods=["GET"])
def calculate():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    fiscal_week = request.args.get('fiscal_week')
    demand = request.args.get('demand')

    try:
        # DemandSettingInitializer(fiscal_week).init()
        demands = [demand]
        # # demands = [IDEAL_DEMAND, TOPDOWN_DEMAND, NORMALIZED_DEMAND, DELTA_DEMAND, FINAL_DEMAND]
        for demand in demands:
            calculator = calculator_factory(demand_name=demand, fiscal_week=fiscal_week)
            demand_result = calculator.calculate_each_week(fiscal_week)
            calculator._save(demand_result.group_by_region(), demand_result.get_df())
        ret[Ret.Data] = 'success'
    except Exception as e:
        logger.error(traceback.format_exc())
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route("/final_demand/save_data_config", methods=["POST"])
def save_data_config():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 操作人信息
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        body_params = request.json
        mix = body_params.get("mix")
        current_week = FiscalWeekContainer().get_current_week()
        fields_to_update = ['lob', 'jd_self_run_mix_cw1', 'jd_self_run_mix_cw2', 'update_time']
        logger.info(f"start update jd config: fiscal_week:{current_week}, operator:{operator}")
        WeeklyDemandSetting.batch_insert(mix, fields_to_update, current_week)
        logger.info(f"finish update jd config: fiscal_week:{current_week}, operator:{operator}")
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/final_demand/get_data_config", methods=["GET"])
def get_data_config():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        current_week = FiscalWeekContainer().get_current_week()
        sub_lob_publish_details = get_sub_lob_publish_detail(current_week)
        sub_lob_list = [item.sub_lob for item in sub_lob_publish_details]
        current_mixs, update_time = DataConfigurationResolver(fiscal_week=current_week, name_type=2, sub_lob_list=sub_lob_list).get_current_week_data()
        update_time = update_time.strftime("%Y-%m-%d %H:%M:%S")
        ret[Ret.Data] = {"data_list": [mix.to_dict() for mix in current_mixs], "update_time": update_time}
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/final_demand/upload_v3", methods=["POST"])
def upload_finalized_demand():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        params = request.args
        fiscal_week = params.get("fiscal_week", "", str)
        file = request.files.get('file')
        if not fiscal_week or file is None:
            raise ErrorExcept(ErrCode.Param, "Param is required, please double check.")

        # 操作人信息
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        logger.info(
            f"demand3.0-->upload ready: fiscal_week:{fiscal_week}, uploader:{operator}")

        ret[Ret.Data] = upload_v3(fiscal_week, file)
        logger.info(
            f"demand3.0-->upload success: fiscal_week:{fiscal_week}, uploader:{operator}")
        return ret

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret
