from kit.compare_data import compare_percentage_strings


RTM_MULTI = "Multi Brand"
RTM_Mono = "Mono Brand"
RTM_CARRIER = "Carrier"
RTM_ONLINE = "ONLINE"
RTM_EDU = "Education"
RTM_ENT = "Enterprise"
RTM_CHANNEL_ONLINE = "C.Online"

rtms = [RTM_Mono, RTM_MULTI, RTM_CHANNEL_ONLINE, RTM_CARRIER, RTM_ENT, RTM_EDU]

DB_RTM_MULTI = 'Multibrand'
DB_RTM_MONO = 'Monobrand'
DB_RTM_CARRIER = 'Carrier'
DB_RTM_EDU = 'EDU'
DB_RTM_CHANNEL_ONLINE = 'Channel Online'
DB_RTM_ENT = 'ENT'

# 数据库 rmt名称对外显示映射关系
rtm_dict = {
    DB_RTM_MULTI: RTM_MULTI,
    DB_RTM_MONO: RTM_Mono,
    DB_RTM_CARRIER: RTM_CARRIER,
    DB_RTM_EDU: RTM_EDU,
    DB_RTM_CHANNEL_ONLINE: RTM_CHANNEL_ONLINE,
    DB_RTM_ENT: RTM_ENT,
}

# 数据库 sub_rmt名称对外显示映射关系
sub_rtm_dict = {
    "EDU": RTM_EDU,
    "ENT": RTM_ENT,
}


# 针对EDU、ENT 临时补充因为前后周缺少HQ的情况
EDU_ENT_HQ = {
    RTM_EDU: {
        "ND": ["Tianhe", "Wanfang"],
        "T1": ["Chuangsen", "Haxi", "Juran Jiaoyu", 'Lexiang', 'Meicheng', 'SX Titi', 'TJ Titi', 'Taihuyun', 'Winsh',
               'Youli Maike', 'Ziguang'],
    },
    RTM_ENT: {
        "T2": ["JD"]
        # "T2": ["JD"],
        # "T1": ['Suodian', 'Zhimao', 'Hecheng', 'Beisheng', 'Fengyun', 'Yixin', 'Shenma', 'Fangzheng'],
        # "ND": ['Jiajie']
    }
}

# UB overall mail 不展示sub_rtm层级的rtm列表
UB_NO_DISPLAY_SUB_RTM = [RTM_EDU, RTM_CHANNEL_ONLINE, RTM_ENT]

SUB_LOB_RULES = ["16 Pro", "16 Cons.", "16e", "15 All", "Others"]
RTMS_RULES = ["Mono Brand", "Multi Brand", "C.Online", "Carrier", "Enterprise", "Education"]
SUB_RTMS_RULES = ["Lifestyle", "Mono", "OTC", "Township", "MM", "Duty-free", "CM", "CU", "CT", "CB"]
SUB_LOB_SORT_RULES = ["All"] + SUB_LOB_RULES
RTMS_SORT_RULES = ["All"] + RTMS_RULES
SUB_RTMS_SORT_RULES = ["All"] + SUB_RTMS_RULES

# rtm、sub_rtm白名单, 去掉了Multi Brand 下的Duty-free
RTMS_SUB_RTM_MAPPING = {
    "Mono Brand": ["Lifestyle", "Mono"],
    "Multi Brand": ["OTC", "Township", "MM"],
    "Carrier": ["CM", "CU", "CT", "CB"],
    "Education": ["EDU"],
    "C.Online": ["Channel Online"],
    "Enterprise": ["ENT"],
}


# todo!!! 注意这里配置, 对于Online和Offline, 如果不想展示Online或者Offline, 设置成空数组就行, 但是因为Offline本身就没有platform
# todo!!! 为了兼容Offline能够正常展示, 在空数组中, 给一个None就行,代表占位！！ eg. 展示则："Offline": [None], 不展示则: "Offline": []
RTMS_SUB_RTM_PLATFORM_MAPPING = {
    "Mono Brand": {
        "Lifestyle": {
            "Online": ['JD', 'Meituan', 'Wechat', 'Douyin', 'Tmall', 'Eleme', 'Others'],
            "Offline": [None]
        },
        "Mono": {
            "Online": ['JD', 'Meituan', 'Wechat', 'Douyin', 'Tmall', 'Eleme', 'Others'],
            "Offline": [None]
        }
    },
    "Multi Brand": {
        "OTC": {
            # "Online": ['JD', 'Meituan', 'Others'], 产品要求: 去掉Online
            "Online": [],
            "Offline": [None]
        },
        "Township": {
            "Online": ['JD', 'Meituan', 'Wechat'],
            "Offline": [None]
        },
        "MM": {
            "Online": ['Others'],
            "Offline": [None]
        },
        # 去掉Duty-free
        # "Duty-free": {
        #     "Online": ['Others'],
        #     "Offline": [None]
        # }
    },
    "Carrier": {
        "CM": {
            "Online": ['JD', 'Tmall', 'Carrier Own', 'Others'],
            "Offline": [None],
            "Unauthorized": [None],
        },
        "CU": {
            "Online": ['JD', 'Tmall', 'Carrier Own', 'Others'],
            "Offline": [None],
            "Unauthorized": [None],
        },
        "CT": {
            "Online": ['JD', 'Tmall', 'Carrier Own', 'Others'],
            "Offline": [None],
            "Unauthorized": [None],
        },
        "CB": {
            # "Online": ['Others'], 产品要求: 去掉Online
            "Online": [],
            "Offline": [None],
            "Unauthorized": [None],
        }
    },
    "Education": {
        "EDU": {
            "Online": ['Others'],
            "Offline": [None]
        },
        # "Campus Experience Center": {
        #     "Online": ['Others'],
        #     "Offline": [None]
        # },
        # "Campus Store": {
        #     "Online": ['Others'],
        #     "Offline": [None]
        # },
        # "MBA Campus": {
        #     "Online": ['Others'],
        #     "Offline": [None]
        # }
    },
    "C.Online": {
        "Channel Online": {
            "Online": ['JD', 'Douyin', 'Tmall', 'Banking', 'Alipay', 'Others'],
            "Offline": []   # Channel Online 只有online, 没有offline
        }
    },
    "Enterprise": {
        "ENT": {
            "Online": ['JD', 'Others'],
            "Offline": [None]
        },
    }
}
ONLINE = 'Online'
OFFLINE = 'Offline'
UNAUTHORIZED = 'Unauthorized'

'''说明
    ≤, 全称:less than or equal to, 缩写:lte,
    lte_7_days_ub: 7天内（含7天）的UB激活量
    lte_3_days_ub: 3天内（含3天）的UB激活量

* 分成四类时间轴来看数据：
    * FY24Q4：上个季度的3天内激活率和7天内激活率，显示上个Q的明细，如FY24Q4
    * QTD：本季度累计到现在的3天内激活率和7天内激活率
    * ≤ 7 days % Weekly Trend ：从本周往前回滚共5周（含本周）的7天内激活率，最早从FY24Q4W8开始，所以，本周五仅有FY24Q4W8-FY24Q4W11的数据
    * D-1 (≤ 7 days % Weekly)：往前回滚1天，该天对应的最后2周的SO对应的7天内激活率，目的是为了对比昨天和前天的数据差异
'''


def format_percentage(numerator, denominator):
    """
        * 百分比前保留1位小数，如7.7% - 不保留小数了
        * 如果数据本身为0，则0%，不用保留1位小数
    """
    if denominator is None or denominator == 0:
        return "-"
    if numerator == 0:
        return "0%"
    percent = (numerator / denominator) * 100
    return f"{round(percent)}%"


class AntiFraudSOUBItem:
    def __init__(self, rtm: str, sub_rtm: str, sub_lob: str, 
                 total_so: int, lte_3_days_ub: int, lte_7_days_ub: int):
        self.rtm = rtm
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.total_so = total_so
        self.lte_3_days_ub = lte_3_days_ub
        self.lte_7_days_ub = lte_7_days_ub


# daily表用来计算qtd的数据
class DailyItem:
    def __init__(self,
                 fiscal_dt: str=None,
                 fiscal_qtr_year_name: str=None,
                 rtm: str=None,
                 sub_rtm: str=None,
                 sub_lob: str=None,
                 total_so: int=None,
                 lte_3_days_ub: int=None,
                 lte_7_days_ub: int=None,
                 fiscal_qtr_year_name_lq: str = None,
                 total_so_lq: int = None,
                 lte_3_days_ub_lq: int = None,
                 lte_7_days_ub_lq: int = None,
                 online_offline: str=None,
                 platform: str=None
    ) -> None:
        self.fiscal_dt = fiscal_dt
        self.fiscal_qtr_year_name = fiscal_qtr_year_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.online_offline = online_offline
        self.platform = platform
        self.sub_lob = sub_lob
        self.total_so = total_so
        self.lte_3_days_ub = lte_3_days_ub
        self.lte_7_days_ub = lte_7_days_ub
        self.fiscal_qtr_year_name_lq = fiscal_qtr_year_name_lq
        self.total_so_lq = total_so_lq
        self.lte_3_days_ub_lq = lte_3_days_ub_lq
        self.lte_7_days_ub_lq = lte_7_days_ub_lq
    
    def lte_3_days(self) -> str:
        return format_percentage(self.lte_3_days_ub, self.total_so)
    
    def lte_7_days(self) -> str:
        return format_percentage(self.lte_7_days_ub, self.total_so)

    def set_total_so(self, total_so):
        self.total_so = total_so

    def add_total_so(self, total_so):
        if total_so is None:
            total_so = 0
        self.total_so += total_so

    def set_lte_3_days_ub(self, lte_3_days_ub):
        self.lte_3_days_ub = lte_3_days_ub

    def add_lte_3_days_ub(self, lte_3_days_ub):
        if lte_3_days_ub is None:
            lte_3_days_ub = 0
        self.lte_3_days_ub += lte_3_days_ub

    def set_lte_7_days_ub(self, lte_7_days_ub):
        self.lte_7_days_ub = lte_7_days_ub

    def add_lte_7_days_ub(self, lte_7_days_ub):
        if lte_7_days_ub is None:
            lte_7_days_ub = 0
        self.lte_7_days_ub += lte_7_days_ub

    def lte_3_days_lq(self) -> str:
        return format_percentage(self.lte_3_days_ub_lq, self.total_so_lq)

    def lte_7_days_lq(self) -> str:
        return format_percentage(self.lte_7_days_ub_lq, self.total_so_lq)

    def set_total_so_lq(self, total_so_lq):
        self.total_so_lq = total_so_lq

    def add_total_so_lq(self, total_so_lq):
        if total_so_lq is None:
            total_so_lq = 0
        self.total_so_lq += total_so_lq

    def set_lte_3_days_ub_lq(self, lte_3_days_ub_lq):
        self.lte_3_days_ub_lq = lte_3_days_ub_lq

    def add_lte_3_days_ub_lq(self, lte_3_days_ub_lq):
        if lte_3_days_ub_lq is None:
            lte_3_days_ub_lq = 0
        self.lte_3_days_ub_lq += lte_3_days_ub_lq

    def set_lte_7_days_ub_lq(self, lte_7_days_ub_lq):
        self.lte_7_days_ub_lq = lte_7_days_ub_lq

    def add_lte_7_days_ub_lq(self, lte_7_days_ub_lq):
        if lte_7_days_ub_lq is None:
            lte_7_days_ub_lq = 0
        self.lte_7_days_ub_lq += lte_7_days_ub_lq

    def set_fiscal_dt(self, fiscal_dt):
        self.fiscal_dt = fiscal_dt

    def set_fiscal_qtr_year_name_lq(self, fiscal_qtr_year_name_lq):
        self.fiscal_qtr_year_name_lq = fiscal_qtr_year_name_lq

    def set_fiscal_qtr_year_name(self, fiscal_qtr_year_name):
        self.fiscal_qtr_year_name = fiscal_qtr_year_name

    def as_dict(self) -> dict:
        return {
            "fiscal_dt": self.fiscal_dt,
            "fiscal_qtr_year_name": self.fiscal_qtr_year_name,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "sub_lob": self.sub_lob,
            "online_offline": self.online_offline,
            "platform": self.platform,
            "total_so": self.total_so,
            "lte_3_days_ub": self.lte_3_days_ub,
            "lte_7_days_ub": self.lte_7_days_ub,
            "lte_3_days": self.lte_3_days(),
            "lte_7_days": self.lte_7_days(),
            "fiscal_qtr_year_name_lq": self.fiscal_qtr_year_name_lq,
            "total_so_lq": self.total_so_lq,
            "lte_3_days_ub_lq": self.lte_3_days_ub_lq,
            "lte_7_days_ub_lq": self.lte_7_days_ub_lq,
            "lte_3_days_lq": self.lte_3_days_lq(),
            "lte_7_days_lq": self.lte_7_days_lq(),
        }

    def __repr__(self):
        return (
            f"DailyItem(fiscal_dt={self.fiscal_dt}, rtm={self.rtm}, sub_rtm={self.sub_rtm}, sub_lob={self.sub_lob}, total_so={self.total_so}, "
            f"lte_3_days_ub={self.lte_3_days_ub}, lte_7_days_ub={self.lte_7_days_ub})")


class WeeklyItem:
    def __init__(self, snapshot_date: str=None,
                 fiscal_qtr_week_name: str=None,
                 rtm: str=None, sub_rtm: str=None,
                 sub_lob: str=None,
                 total_so: int=None, lte_7_days_ub: int=None,
                 online_offline: str=None, platform: str=None
    ) -> None:
        self.snapshot_date = snapshot_date
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.online_offline = online_offline
        self.platform = platform
        self.sub_lob = sub_lob
        self.total_so = total_so
        self.lte_7_days_ub = lte_7_days_ub
    
    def lte_7_days(self) -> str:
        return format_percentage(self.lte_7_days_ub, self.total_so)

    def set_total_so(self, total_so):
        self.total_so = total_so

    def set_lte_7_days_ub(self, lte_7_days_ub):
        self.lte_7_days_ub = lte_7_days_ub
        
    def lte_threshold_class_name(self) -> str:
        return "highlightRed" if compare_percentage_strings(self.lte_7_days(), '80%') < 0 else ""

    def as_dict(self) -> dict:
        return {
            "snapshot_date": self.snapshot_date,
            "fiscal_qtr_week_name": self.fiscal_qtr_week_name,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "sub_lob": self.sub_lob,
            "online_offline": self.online_offline,
            "platform": self.platform,
            "total_so": self.total_so,
            "lte_7_days_ub": self.lte_7_days_ub,
            "lte_7_days": self.lte_7_days(),
            "lte_threshold_class_name": self.lte_threshold_class_name()
        }

    def __repr__(self):
        return (
            f"WeeklyItem(snapshot_date={self.snapshot_date}, "
            f"fiscal_qtr_week_name={self.fiscal_qtr_week_name}, rtm={self.rtm}, sub_rtm={self.sub_rtm}, sub_lob={self.sub_lob},"
            f" total_so={self.total_so}, lte_7_days_ub={self.lte_7_days_ub})")


# 做层级汇总，需要所有的数据，list[]
# 第一层：加和 China Channel
# 第二层sub_rtm加和
# 第三层是原始数据
'''

'''
class AntiFraudSOUBWeeklySummary:
    def __init__(self, weekly_data: list[WeeklyItem]):
        self.weekly_data = weekly_data
    
    def _total(self):
        for data in self.weekly_data:
            pass
            
          
            

class AntiFraudSOUBEmailView:
    def __init__(self, view_type: str, rtm: str, sub_rtm: str, sub_lob: str) -> None:
        self.view_type = view_type
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        
        self.qtd_lte_3_days = 0
        self.qtd_lte_7_days = 0
        # weekly 
        self.weekly_lte_7_days_rollback_w1 = 0
        self.weekly_lte_7_days_rollback_w2 = 0
        self.weekly_lte_7_days_rollback_w3 = 0
        self.weekly_lte_7_days_rollback_w4 = 0
        self.weekly_lte_7_days_rollback_w5 = 0
        
        # D-1 <=7 Days % 最大数据日期对应的上一天，对应的最后两周的指标值
        self.d1_lte_7_days_latest_w1 = 0
        self.d1_lte_7_days_latest_w2 = 0
    
    def set_qtd(self, qtd_lte_3_days: float, qtd_lte_7_days: float) -> None:
        self.qtd_lte_3_days = qtd_lte_3_days
        self.qtd_lte_7_days = qtd_lte_7_days
    
    def set_weekly(self, weekly_lte_7_days_roll_back_w1: float, weekly_lte_7_days_roll_back_w2: float, weekly_lte_7_days_roll_back_w3: float, weekly_lte_7_days_roll_back_w4: float, weekly_lte_7_days_roll_back_w5: float) -> None:
        self.weekly_lte_7_days_roll_back_w1 = weekly_lte_7_days_roll_back_w1
        self.weekly_lte_7_days_roll_back_w2 = weekly_lte_7_days_roll_back_w2
        self.weekly_lte_7_days_roll_back_w3 = weekly_lte_7_days_roll_back_w3
        self.weekly_lte_7_days_roll_back_w4 = weekly_lte_7_days_roll_back_w4
        self.weekly_lte_7_days_roll_back_w5 = weekly_lte_7_days_roll_back_w5
    
    def set_d1(self, d1_lte_7_days_latest_w1: float, d1_lte_7_days_latest_w2: float) -> None:
        self.d1_lte_7_days_latest_w1 = d1_lte_7_days_latest_w1
        self.d1_lte_7_days_latest_w2 = d1_lte_7_days_latest_w2
    
    def level(self):
        return 1
    
    def first_column(self)->str:
        return 'China Channel'
    
    def __str__(self) -> str:
        return f"rtm: {self.rtm}, sub_rtm: {self.sub_rtm}, sub_lob: {self.sub_lob}, qtd_lte_3_days: {self.qtd_lte_3_days}, qtd_lte_7_days: {self.qtd_lte_7_days}, weekly_lte_7_days_roll_back_w1: {self.weekly_lte_7_days_roll_back_w1}, weekly_lte_7_days_roll_back_w2: {self.weekly_lte_7_days_roll_back_w2}, weekly_lte_7_days_roll_back_w3: {self.weekly_lte_7_days_roll_back_w3}, weekly_lte_7_days_roll_back_w4: {self.weekly_lte_7_days_roll_back_w4}, weekly_lte_7_days_roll_back_w5: {self.weekly_lte_7_days_roll_back_w5}, d1_lte_7_days_latest_w1: {self.d1_lte_7_days_latest_w1}, d1_lte_7_days_latest_w2: {self.d1_lte_7_days_latest_w2}"
    
    def as_dict(self) -> dict:
        return {
            "level": self.level(),
            "first_clomun": self.first_column(),
            "qtd_lte_3_days": self.qtd_lte_3_days,
            "qtd_lte_7_days": self.qtd_lte_7_days,
            "weekly_lte_7_days_roll_back_w1": self.weekly_lte_7_days_roll_back_w1,
            "weekly_lte_7_days_roll_back_w2": self.weekly_lte_7_days_roll_back_w2,
            "weekly_lte_7_days_roll_back_w3": self.weekly_lte_7_days_roll_back_w3,
            "weekly_lte_7_days_roll_back_w4": self.weekly_lte_7_days_roll_back_w4,
            "weekly_lte_7_days_roll_back_w5": self.weekly_lte_7_days_roll_back_w5,
            "d1_lte_7_days_latest_w1": self.d1_lte_7_days_latest_w1,
            "d1_lte_7_days_latest_w2": self.d1_lte_7_days_latest_w2,
        }


class DailyNdItem:
    def __init__(self,
                 fiscal_dt: str = None,
                 fiscal_qtr_year_name: str = None,
                 rtm: str = None, sub_rtm: str = None,
                 hq_name: str = None,
                 sub_lob: str = None,
                 total_so: int = None,
                 ub3: int = None,
                 ub7: int = None,
                 nd_type: str = None,
                 fiscal_qtr_year_name_lq: str = None,
                 total_so_lq: int = None,
                 ub3_lq: int = None,
                 ub7_lq: int = None,
                 ) -> None:
        self.fiscal_dt = fiscal_dt
        self.fiscal_qtr_year_name = fiscal_qtr_year_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.hq_name = hq_name
        self.sub_lob = sub_lob
        self.total_so = total_so
        self.lte_3_days_ub = ub3
        self.lte_7_days_ub = ub7
        self.nd_type = nd_type

        self.fiscal_qtr_year_name_lq = fiscal_qtr_year_name_lq
        self.total_so_lq = total_so_lq
        self.lte_3_days_ub_lq = ub3_lq
        self.lte_7_days_ub_lq = ub7_lq

    def lte_3_days(self) -> str:
        return format_percentage(self.lte_3_days_ub, self.total_so)

    def lte_7_days(self) -> str:
        return format_percentage(self.lte_7_days_ub, self.total_so)

    def lte_3_days_lq(self) -> str:
        return format_percentage(self.lte_3_days_ub_lq, self.total_so_lq)

    def lte_7_days_lq(self) -> str:
        return format_percentage(self.lte_7_days_ub_lq, self.total_so_lq)

    def set_total_so(self, total_so):
        self.total_so = total_so

    def set_total_so_lq(self, total_so_lq):
        self.total_so_lq = total_so_lq

    def set_lte_3_days_ub(self, lte_3_days_ub):
        self.lte_3_days_ub = lte_3_days_ub

    def set_lte_7_days_ub(self, lte_7_days_ub):
        self.lte_7_days_ub = lte_7_days_ub

    def set_lte_3_days_ub_lq(self, lte_3_days_ub_lq):
        self.lte_3_days_ub_lq = lte_3_days_ub_lq

    def set_lte_7_days_ub_lq(self, lte_7_days_ub_lq):
        self.lte_7_days_ub_lq = lte_7_days_ub_lq

    def set_fiscal_dt(self, fiscal_dt):
        self.fiscal_dt = fiscal_dt

    def as_dict(self) -> dict:
        return {
            "fiscal_dt": self.fiscal_dt,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "fiscal_qtr_year_name": self.fiscal_qtr_year_name,
            "hq_name": self.hq_name,
            "nd_type": self.nd_type,
            "sub_lob": self.sub_lob,
            "total_so": self.total_so,
            "lte_3_days_ub": self.lte_3_days_ub,
            "lte_7_days_ub": self.lte_7_days_ub,
            "lte_3_days": self.lte_3_days(),
            "lte_7_days": self.lte_7_days(),
            "fiscal_qtr_year_name_lq": self.fiscal_qtr_year_name_lq,
            "total_so_lq": self.total_so_lq,
            "lte_3_days_ub_lq": self.lte_3_days_ub_lq,
            "lte_7_days_ub_lq": self.lte_7_days_ub_lq,
            "lte_3_days_lq": self.lte_3_days_lq(),
            "lte_7_days_lq": self.lte_7_days_lq()
        }


class WeeklyNdItem:
    def __init__(self,
                 snapshot_date: str = None,
                 fiscal_qtr_week_name: str = None,
                 rtm: str = None, sub_rtm: str = None,
                 sub_lob: str = None,
                 hq_name: str = None,
                 nd_type: str = None,
                 total_so: int = None, lte_7_days_ub: int = None
                 ) -> None:
        self.snapshot_date = snapshot_date
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.hq_name = hq_name
        self.nd_type = nd_type
        self.sub_lob = sub_lob
        self.total_so = total_so
        self.lte_7_days_ub = lte_7_days_ub

    def lte_7_days(self) -> str:
        return format_percentage(self.lte_7_days_ub, self.total_so)

    def set_total_so(self, total_so):
        self.total_so = total_so

    def set_lte_7_days_ub(self, lte_7_days_ub):
        self.lte_7_days_ub = lte_7_days_ub

    def lte_threshold_class_name(self) -> str:
        return "highlightRed" if compare_percentage_strings(self.lte_7_days(), '80%') < 0 else ""

    def as_dict(self) -> dict:
        return {
            "snapshot_date": self.snapshot_date,
            "fiscal_qtr_week_name": self.fiscal_qtr_week_name,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "sub_lob": self.sub_lob,
            "hq_name": self.hq_name,
            "nd_type": self.nd_type,
            "total_so": self.total_so,
            "lte_7_days_ub": self.lte_7_days_ub,
            "lte_7_days": self.lte_7_days(),
            "lte_threshold_class_name": self.lte_threshold_class_name()
        }
