from domain.suspension.entity.suspension_datasource_file_info import SuspensionDataSourceFileEntity
from domain.suspension.entity.suspension_review_upload_file_info import SuspensionReviewUploadEntity


class ConfirmInfo:
    def __init__(self, data: list, file_info, upload_date: str):
        self.upload_date = upload_date
        self.data_list = self._build_data_list(data)
        self.file_info = self._build_file(file_info)

    def _build_data_list(self, data):
        data_list = []
        for item in data:
            data_list.append(
                SuspensionReviewUploadEntity(
                    baidu_cross_check_result=item.get("baidu_cross_check_result"),
                    check_date=item.get("check_date"),
                    client_ip_address=item.get("client_ip_address"),
                    compliance_cross_check=item.get("compliance_cross_check"),
                    compliance_judge=item.get("compliance_judge"),
                    dmp_adjust_data_in_sandbox=item.get("dmp_adjust_data_in_sandbox"),
                    dmp_judgment=item.get("dmp_judgment"),
                    is_active=item.get("is_active"),
                    lob=item.get("lob"),
                    pos_id=item.get("pos_id"),
                    pos_name=item.get("pos_name"),
                    random_id=item.get("random_id"),
                    sn=item.get("sn"),
                    so_city=item.get("so_city"),
                    so_date=item.get("so_date"),
                    so_fiscal_week=item.get("so_fiscal_week"),
                    so_province=item.get("so_province"),
                    store_type=item.get("store_type"),
                    sub_lob=item.get("sub_lob"),
                    sub_rtm=item.get("sub_rtm"),
                    t1_disti_id=item.get("t1_disti_id"),
                    t1_disti_name=item.get("t1_disti_name"),
                    t2_hq_id=item.get("t2_hq_id"),
                    t2_reseller_name=item.get("t2_reseller_name"),
                    tencent_cross_check_result=item.get("tencent_cross_check_result"),
                    ub_city=item.get("ub_city")
                )
            )
        return data_list

    def _build_file(self, file_info):
        return SuspensionDataSourceFileEntity(
            filename=file_info.get('origin_file_name'),
            upload_by=file_info.get('upload_by'),
            upload_date=self.upload_date,
            url=file_info.get('file_path')
        )
