SUSPENSION_REVIEW_DICT = {
    "sub_rtm": "sub_rtm",
    "Disti/T1 ID": "t1_disti_id",
    "Disti/T1 name": "t1_disti_name",
    "T2 HQ ID": "t2_hq_id",
    "T2 Reseller name": "t2_reseller_name",
    "pos_id": "pos_id",
    "pos_name": "pos_name",
    "store_type": "store_type",
    "pos_active": "is_active",
    "lob": "lob",
    "sub_lob": "sub_lob",
    "so_fiscal_week": "so_fiscal_week",
    "so_date": "so_date",
    "CLIENT_IP_ADDR_TXT": "client_ip_address",
    "SN": "sn",
    "random_id": "random_id",
    "so_province": "so_province",
    "so_city": "so_city",
    "ub_city": "ub_city",
    "Cross-Check (Baidu)": "baidu_cross_check_result",
    "Cross-Check (Tencent)": "tencent_cross_check_result",
    "DMP Judge": "dmp_judgment",
    "Compliance cross-check": "compliance_cross_check",
    "Compliance Judge": "compliance_judge",  # Support/Reject/support/reject
    "DMP adjust data in sandbox": "dmp_adjust_data_in_sandbox",
    "check_date": "check_date"
}


class ComplianceJudgmentValues:
    SUPPORT = "Support"
    REJECT = "Reject"
    SUPPORT_LOWER = "support"
    REJECT_LOWER = "reject"


VALID_COMPLIANCE_VALUES = {ComplianceJudgmentValues.SUPPORT,
                           ComplianceJudgmentValues.REJECT,
                           ComplianceJudgmentValues.SUPPORT_LOWER,
                           ComplianceJudgmentValues.REJECT_LOWER}


SUSPENSION_APPEAL_UPLOAD_TYPE = 'Suspension Appeal Review'
