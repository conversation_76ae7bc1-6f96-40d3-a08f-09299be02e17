from domain.suspension.entity.const import SUSPENSION_APPEAL_UPLOAD_TYPE


class SuspensionDataSourceFileEntity:
    def __init__(self,
                 filename: str,
                 upload_by: str,
                 upload_date: str,
                 url: str,
                 status: int = 1,
                 datasource_type: str = SUSPENSION_APPEAL_UPLOAD_TYPE
                 ) -> None:
        self.datasource_type = datasource_type
        self.filename = filename
        self.upload_by = upload_by
        self.upload_date = upload_date
        self.url = url
        self.status = status

