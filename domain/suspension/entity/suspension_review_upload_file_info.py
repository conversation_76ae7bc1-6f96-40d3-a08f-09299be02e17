class SuspensionReviewUploadEntity:
    def __init__(self,
                 baidu_cross_check_result: str, check_date: str, client_ip_address: str, compliance_cross_check: str,
                 compliance_judge: str, dmp_adjust_data_in_sandbox: str, dmp_judgment: str, is_active: str, lob: str,
                 pos_id: str, pos_name: str, random_id: str, sn: str,  so_city: str, so_date: str, so_fiscal_week: str,
                 so_province: str, store_type: str, sub_lob: str, sub_rtm: str, t1_disti_id: str, t1_disti_name: str,
                 t2_hq_id: str, t2_reseller_name: str, tencent_cross_check_result: str, ub_city: str) -> None:
        self.baidu_cross_check_result = baidu_cross_check_result
        self.check_date = check_date
        self.client_ip_address = client_ip_address
        self.compliance_cross_check = compliance_cross_check
        self.compliance_judge = compliance_judge
        self.dmp_adjust_data_in_sandbox = dmp_adjust_data_in_sandbox
        self.dmp_judgment = dmp_judgment
        self.is_active = is_active
        self.lob = lob
        self.pos_id = pos_id
        self.pos_name = pos_name
        self.random_id = random_id
        self.sn = sn
        self.so_city = so_city
        self.so_date = so_date
        self.so_fiscal_week = so_fiscal_week
        self.so_province = so_province
        self.store_type = store_type
        self.sub_lob = sub_lob
        self.sub_rtm = sub_rtm
        self.t1_disti_id = t1_disti_id
        self.t1_disti_name = t1_disti_name
        self.t2_hq_id = t2_hq_id
        self.t2_reseller_name = t2_reseller_name
        self.tencent_cross_check_result = tencent_cross_check_result
        self.ub_city = ub_city


    def to_dict(self):
        return {
            "baidu_cross_check_result": self.baidu_cross_check_result,
            "check_date": self.check_date,
            "client_ip_address": self.client_ip_address,
            "compliance_cross_check": self.compliance_cross_check,
            "compliance_judge": self.compliance_judge,
            "dmp_adjust_data_in_sandbox": self.dmp_adjust_data_in_sandbox,
            "dmp_judgment": self.dmp_judgment,
            "is_active": self.is_active,
            "lob": self.lob,
            "pos_id": self.pos_id,
            "pos_name": self.pos_name,
            "random_id": self.random_id,
            "sn": self.sn,
            "so_city": self.so_city,
            "so_date": self.so_date,
            "so_fiscal_week": self.so_fiscal_week,
            "so_province": self.so_province,
            "store_type": self.store_type,
            "sub_lob": self.sub_lob,
            "sub_rtm": self.sub_rtm,
            "t1_disti_id": self.t1_disti_id,
            "t1_disti_name": self.t1_disti_name,
            "t2_hq_id": self.t2_hq_id,
            "t2_reseller_name": self.t2_reseller_name,
            "tencent_cross_check_result": self.tencent_cross_check_result,
            "ub_city": self.ub_city,
        }