import datetime

from data.datasource_data import DataSourceFile
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.suspension.suspension_review_upload_record import SuspensionReviewUploadRecord
from domain.suspension.entity.confrim_entity import ConfirmInfo
from kit.transaction.transaction import transactional
from util.fast_lite_base import FASTLiteSession


class UploadImpl:
    def __init__(self, data: list):
        self.data = data
        self.datasource_repository = self.get_datasource_repository()
        self.upload_record_repository = self.get_upload_record_repository()
        self._session = self._session()

    def get_session(self):
        return self._session

    def _session(self):
        return FASTLiteSession()

    def get_datasource_repository(self):
        return DataSourceFile

    def get_upload_record_repository(self):
        return SuspensionReviewUploadRecord

    @transactional
    def do_biz_with_transaction(self):
        now = datetime.datetime.now()
        # data: 多文件上传
        for item in self.data:
            # datasource 记录入库
            self.datasource_repository.insert(data=item.file_info, db_session=self._session, now=now)

            # 真实数据入库
            self.upload_record_repository.batch_insert(objs=item.data_list, db_session=self._session, now=now)


def upload_review_confirm(data: list):
    # 获取当前时间所在周
    fiscal_day_info = FiscalYearWeek.get_fiscal_by_date(date=datetime.datetime.now().strftime('%Y-%m-%d'))
    fiscal_week = fiscal_day_info[0].fiscal_qtr_week_name if fiscal_day_info else None

    # build entity
    confirm_data = []
    for item in data:
        confirm_data.append(
            ConfirmInfo(data=item.get('data_list', list()), file_info=item.get('file_info', dict()), upload_date=fiscal_week)
        )

    # 入库
    UploadImpl(data=confirm_data).do_biz_with_transaction()



