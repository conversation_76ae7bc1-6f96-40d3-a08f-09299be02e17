import os

import pandas
import pandas as pd

from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.databend.dashboard.demand_esr_ub_eoh import DemandEsrUbEoh
from data.mysqls.demand.demand_by_region_pool import Demand<PERSON>yRegionPool
from data.mysqls.demand.demand_by_soldto_pool import <PERSON><PERSON><PERSON><PERSON><PERSON>oldtoPool
from data.mysqls.demand.demand_x_setting import IdealDemandXValueSetting
from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from data.mysqls.demand.sellin_demand_woi_setting import SellinDemandWoiSetting
from domain.demand.entity.const import CHINA_MAINLAND, RTMS, IDEAL_DEMAND, NO_MENTION_SOLDTO, SELL_IN_DEMAND
from domain.demand.impl.processor.const import SALES_FORECAST_PROCESSOR
from domain.demand.impl.processor.processor import Processor
from domain.demand.impl.state_machine import StateProxy
from domain.supply.entity import Lob
from kit.pd import fill_nan_to_none
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.send_email import async_rate_limited_send_email


class WoiProcessor(Processor):

    def __init__(self, processor: str, fiscal_week: str, woi_type: str):
        max_do_times = -1 # 一周可无限次执行
        self.woi_type = woi_type
        super().__init__(processor, fiscal_week, max_do_times)

    def _can_do(self) -> bool:
        return True

    def _do(self):
        fiscal_week = self.fiscal_week
        # 上传的 rtm sales forecast 数据
        region_pool = DemandByRegionPool.get_by_fiscal_week(fiscal_week)
        region_pool = region_pool[['fiscal_week', 'region', 'sub_lob', 'mpn']]

        # 获取woi设置
        woi_by_mpns, woi_by_sublobs = SellinDemandWoiSetting.query_publish_woi_by_fiscal_week(fiscal_week, self.woi_type)
        # 判断woi_by_mpns和woi_by_sublobs是否为空
        if not woi_by_mpns or not woi_by_sublobs:
            raise Exception('woi is not ready')

        # 将对象列表转换为字典列表
        woi_by_sublobs_dict = [obj.__dict__ for obj in woi_by_sublobs]
        woi_by_mpns_dict = [obj.__dict__ for obj in woi_by_mpns]

        # 创建 DataFrame
        woi_by_sublobs_df = pd.DataFrame(woi_by_sublobs_dict)
        woi_by_mpns_df = pd.DataFrame(woi_by_mpns_dict)

        # 根据sub_lob，合并origin_df，woi_by_sublobs
        region_pool = pd.merge(region_pool, woi_by_sublobs_df, on='sub_lob', how='left')
        region_pool = region_pool.rename(columns={'woi_max':'woi_by_sublob_max',
                                                  'woi_max_cw2':'woi_by_sublob_max_cw2',
                                                  'woi_max_cw3':'woi_by_sublob_max_cw3'})
        # 根据mpn，合并origin_df，woi_by_mpns
        region_pool = pd.merge(region_pool, woi_by_mpns_df, on=['sub_lob', 'mpn'], how='left')
        region_pool = region_pool.rename(columns={'woi_max': 'woi_by_mpn_max', 'woi_min': 'woi_by_mpn_min'})
        region_pool = region_pool.rename(columns={'woi_max_cw2': 'woi_by_mpn_max_cw2', 'woi_min_cw2': 'woi_by_mpn_min_cw2'})
        region_pool = region_pool.rename(columns={'woi_max_cw3': 'woi_by_mpn_max_cw3', 'woi_min_cw3': 'woi_by_mpn_min_cw3'})

        fields_to_update = ['woi_by_mpn_max', 'woi_by_mpn_min', 'woi_by_mpn_max_cw2', 'woi_by_mpn_min_cw2',
                            'woi_by_mpn_max_cw3', 'woi_by_mpn_min_cw3', 'woi_by_sublob_max', 'woi_by_sublob_max_cw2',
                            'woi_by_sublob_max_cw3', 'update_time']

        region_pool = fill_nan_to_none(region_pool)
        update_list = list(region_pool.to_dict(orient='records'))
        DemandByRegionPool.batch_update(update_list, fields_to_update)
