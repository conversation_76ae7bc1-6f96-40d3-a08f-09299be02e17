import traceback
from functools import wraps
from sqlalchemy.orm import Session

from util.conf import logger


def transactional(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        session: Session = self.get_session()
        session.begin()
        if session is None:
            raise ValueError("Session is required for transactional operation")

        try:
            # 执行事务操作
            result = func(self, *args, **kwargs)
            # 提交事务
            session.commit()
            return result
        except Exception as e:
            # 发生异常时回滚事务
            session.rollback()
            logger.error(f"Transaction failed: {traceback.format_exc()}")
            raise e
        finally:
            session.close()

    return wrapper
