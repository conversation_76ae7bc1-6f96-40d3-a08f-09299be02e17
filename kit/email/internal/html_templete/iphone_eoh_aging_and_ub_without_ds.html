<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <title>
    </title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        #outlook a {
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }

    </style>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
    <style>
        .mj-outlook-group-fix { width:100% !important; }
    </style>
    <![endif]-->
    <style>
        @media only screen and (min-width:480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }
        }
        @media only screen and (min-width:600px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }
        }

    </style>
    <style media="screen and (min-width:480px)">
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%;
        }

    </style>
    <style media="screen and (min-width:600px)">
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%;
        }

    </style>
    <style>
    </style>
    <style>
        /* @media screen and (max-width:600px) { */
        .qtd-perf:not(.ub-table) .first-tr th:last-child {
            text-align: center;
        }
        .timeTip {
            font-size: 10px;
            font-weight: 400;
            line-height: 12px;
        }
        .timeTip.highlightRed.noPadding {
            color: #F63F54;
            padding-top: 0;
        }
        .tableTitle {
            font-size: 14px;
            font-weight: 500;
            padding: 24px 0 12px 8px;
            line-height: 20px;
        }
        .qtd-perf tr td.highlightRed,
        .qtd-perf tr td.highlightRed div {
            color: #F63F54;
            font-weight: 500;
        }
        .qtd-perf .three.highlightBlue.pro td,
        .qtd-perf .three.highlightBlue.cons td {
            padding-top: 4px;
            padding-bottom: 4px;
            font-weight: 500;
        }
        .qtd-perf .two:not(:has(+ .two)):has(+ .three.pro) + tr td,
        .qtd-perf .three.highlightBlue.cons:not(:has(+ .cons)) + tr td {
            padding-top: 4px;
        }
        .qtd-perf .two:not(:has(+ .two)):has(+ .three.pro) td {
            padding-bottom: 4px;
        }
        .widthFixed .first-tr th:first-child {
            width: 18%
        }
        .widthFixed .first-tr th:nth-child(2), .widthFixed .first-tr th:nth-child(3){
            width: 11%;
        }
        .qtd-perf tr:not(.three) td:first-child {
            white-space: normal;
        }
        /* } */
    </style>
    <style>
        @media (prefers-color-scheme: dark) {
            .tableWrapper>table {
                padding: 16px;
                background: #232425;
            }

            .header {
                background-color: rgba(254, 254, 254, 1);
                color: #1C1C1E;
                border-radius: 12px;
                position: relative;
            }

            .header-text {
                padding: 0 6px;
            }

            .header .apple-icon {
                background-image: url(data:image/png;base64,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);
            }

            .header .apple-icon {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 90px;
                height: 60px;
                display: block;
                z-index: 0;
            }

            .sub-title {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
            }

            .sub-title p {
                margin: 6px 0 0;
            }

            .apple-icon,
            .title,
            .sub-title {
                color: #1C1C1E;
            }

            .icon {
                color: #1C1C1E;
            }

            .title {
                position: relative;
                left: 2px;
            }

            .date {
                font-size: 10px;
                color: #6E6E73;
                padding: 0 2px;
                line-height: 12px;
            }

            .type-btn-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .type-btn-title p:first-child {
                font-weight: 700;
                margin: 0;
                font-size: 16px;
                line-height: 20px;
                color: #fff;
            }

            .type-btn-title p:last-child {
                font-size: 10px;
                color: #aeaeb2;
            }

            .wrap-box-div {
                padding: 24px 0 0 0;
            }

            .timeTip {
                color: #aeaeb2;
            }
            .lightTip {
                color: rgba(255, 149, 0);
            }
            table {
                border-collapse: separate;
            }

            .tableTitle {
                color: #fff;
            }

            .first-tr {
                background: #313136;
            }

            .first-tr th {
                vertical-align: middle;
                border-top: 1px solid #6e6e73;
            }

            .first-tr th:first-child {
                border-top-left-radius: 12px;
                border-left: 1px solid #6e6e73;
            }
            .qtd-perf:not(.ub-table) .first-tr th:first-child,
            .qtd-perf:not(.ub-table) .first-tr th:nth-child(3),
            .qtd-perf:not(.ub-table) .two-tr th:nth-child(1),
            .qtd-perf:not(.ub-table) tr td:first-child,
            .qtd-perf:not(.ub-table) td:nth-child(3),
            .qtd-perf:not(.ub-table) td:nth-child(4) {
                border-right: 1px solid #6e6e73;
            }

            .qtd-perf tr td:last-child {
                border-right: 1px solid #6e6e73;
            }

            .first-tr th:last-child {
                border-top-right-radius: 12px;
                border-right: 1px solid #6e6e73;
            }

            .qtd-perf tr td:first-child {
                border-left: 1px solid #6e6e73;
            }

            .two-tr th:last-child {
                border-right: 1px solid #6e6e73;
            }

            .qtd-perf tr:last-child td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf tr:last-child td:first-child {
                border-bottom-left-radius: 12px;
            }

            .qtd-perf tr:last-child td:last-child {
                border-bottom-right-radius: 12px;
            }

            .qtd-perf th,
            .qtd-perf td {
                font-weight: 400;
                border-bottom: 1px solid #6e6e73;
                line-height: 10px;
                padding: 10px 8px;
            }

            .qtd-perf tr td:last-child {
                border-right: 1px solid #6e6e73;
            }

            .qtd-perf td {
                vertical-align: top;
                color: #fff;
                white-space: nowrap;
            }

            .qtd-perf td div {
                padding-top: 3px;
                color: #6E6E73;
            }

            .qtd-perf tr:not(.three) td:first-child {
                white-space: nowrap;
            }

            .qtd-perf th {
                font-size: 8px;
                color: #aeaeb2;
                white-space: nowrap;
                background: #313136;
                padding: 8px;
                text-align: left;
            }

            .qtd-perf .one td {
                font-size: 10px;
                font-weight: 500;
                line-height: 12px;
            }

            .qtd-perf .two td {
                color: #fff;
                font-size: 9px;
            }

            .qtd-perf .three td {
                color: #AEAEB2;
                font-size: 9px;
                padding: 10px 0 0 8px;
                word-break: keep-all;
            }

            .qtd-perf .three td,
            .qtd-perf .three td {
                color: #aeaeb2;
            }

            .qtd-perf .four td {
                color: #aeaeb2;
                font-size: 9px;
                padding: 10px 0 0 8px;
                border-bottom: none;
            }

            .qtd-perf .three:not(:has(+ .three)):not(:has(+ .four)) td {
                border-bottom: 1px solid #6e6e73;
                padding-bottom: 10px;
            }

            .qtd-perf .four:not(:has(+ .four)):not(:has(+ .three)) td {
                border-bottom: 1px solid #6e6e73;
                padding-bottom: 10px;
            }

            .qtd-perf .four td:nth-child(1) {
                padding-left: 18px;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .three) td,
            .qtd-perf .two:not(:has(+ .two)):has(+ .four) td {
                padding-bottom: 0;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .one) td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf .three:not(:has(+ .three)):has(+ .two) td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf .two:has(+ .two) td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf .three td:nth-child(1) {
                padding-left: 13px;
            }

            .qtd-perf .two td,
            .qtd-perf .three td {
                border-bottom: none;
            }

            .qtd-perf .two:first-child td,
            .qtd-perf .three:first-child td,
            .qtd-perf .four:first-child td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf.highlightTable tr.highlightBlue {
                background: #212930;
                font-weight: 500;
            }

            .footer {
                background-color: #34343b;
                border-radius: 12px;
            }

            .footer-hint {
                color: #d1d1d6;
                font-size: 10px;
                line-height: 16px;
            }

            .footer-hint-text {
                color: #4F78E3;
                text-decoration: none;
            }

            .hint-text {
                color: #8e8e93;
                text-align: center;
                font-size: 10px;
                font-weight: 400;
                line-height: 14px;
            }
        }

        @media (prefers-color-scheme: light) {
            .tableWrapper>table {
                background: #fff;
                padding: 16px;
            }

            .header {
                background-color: #1C1C1E;
                border-radius: 12px;
                position: relative;
            }

            .header-text {
                padding: 0 6px;
            }

            .header .apple-icon {
                background-image: url(data:image/png;base64,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);
                position: absolute;
                right: 0;
                bottom: 0;
                width: 90px;
                height: 60px;
                display: block;
                z-index: 0;
            }

            .icon {
                color: #fff;
            }

            .apple-icon {
                color: #fff;
            }

            .title {
                position: relative;
                left: 2px;
                color: #fff;
            }

            .sub-title {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #fff;
            }

            .sub-title p {
                margin: 6px 0 0;
            }

            .date {
                font-size: 10px;
                color: #6E6E73;
                padding: 0 2px;
                line-height: 12px;
            }

            table {
                border-collapse: separate;
            }

            .tableTitle {
                color: #1c1c1e;
            }

            .wrap-box-div {
                padding: 24px 0 0 0;
            }

            .timeTip {
                color: #aeaeb2;
            }
            .lightTip {
                color: #ff9500;
            }
            .type-btn-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .type-btn-title p:first-child {
                font-weight: 700;
                margin: 0;
                font-size: 16px;
                line-height: 20px;
                color: #1C1C1E;
            }

            .type-btn-title p:last-child {
                font-size: 10px;
                color: #aeaeb2;
            }

            .first-tr {
                background: #f5f5f7;
            }

            .first-tr th {
                vertical-align: middle;
                border-top: 1px solid #E5E5EA;
            }

            .first-tr th:first-child {
                border-top-left-radius: 12px;
                border-left: 1px solid #E5E5EA;
            }

            .qtd-perf:not(.ub-table) .first-tr th:first-child,
            .qtd-perf:not(.ub-table) .first-tr th:nth-child(3),
            .qtd-perf:not(.ub-table) .two-tr th:nth-child(1),
            .qtd-perf:not(.ub-table) tr td:first-child,
            .qtd-perf:not(.ub-table) td:nth-child(3),
            .qtd-perf:not(.ub-table) td:nth-child(4) {
                border-right: 1px solid #E5E5EA;
            }

            .first-tr th:last-child {
                border-top-right-radius: 12px;
                border-right: 1px solid #E5E5EA;
            }

            .two-tr th:last-child {
                border-right: 1px solid #E5E5EA;
            }

            .qtd-perf tr td:first-child {
                border-left: 1px solid #E5E5EA;
            }

            .qtd-perf tr td:last-child {
                border-right: 1px solid #E5E5EA;
            }

            .qtd-perf tr:last-child td {
                border-bottom: 1px solid #E5E5EA;
            }

            .qtd-perf tr:last-child td:first-child {
                border-bottom-left-radius: 12px;
            }

            .qtd-perf tr:last-child td:last-child {
                border-bottom-right-radius: 12px;
            }

            .qtd-perf th,
            .qtd-perf td {
                border-bottom: 1px solid #E5E5EA;
                font-weight: 400;
                line-height: 10px;
                padding: 10px 8px;
            }

            .qtd-perf td {
                vertical-align: top;
                color: #3a3a3c;
                white-space: nowrap;
            }

            .qtd-perf td div {
                padding-top: 3px;
                color: #aeaeb2;
            }

            .qtd-perf th {
                font-size: 8px;
                color: #6e6e73;
                white-space: nowrap;
                background: #f5f5f7;
                padding: 8px;
                text-align: left;
            }

            .qtd-perf .one td {
                font-size: 10px;
                font-weight: 500;
                line-height: 12px;
            }

            .qtd-perf .two td {
                font-size: 9px;
                color: #3a3a3c;
            }

            .qtd-perf .three td {
                color: #6E6E73;
                font-size: 9px;
                padding: 10px 0 0 8px;
                word-break: keep-all;
            }

            .qtd-perf .four td {
                color: #6e6e73;
                font-size: 9px;
                padding: 10px 0 0 8px;
                border-bottom: none;
            }

            .qtd-perf .three:not(:has(+ .three)):not(:has(+ .four)) td {
                border-bottom: 1px solid #e5e5ea;
                padding-bottom: 10px;
            }

            .qtd-perf .four:not(:has(+ .four)):not(:has(+ .three)) td {
                border-bottom: 1px solid #e5e5ea;
                padding-bottom: 10px;
            }

            .qtd-perf .four td:nth-child(1) {
                padding-left: 18px;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .one) td {
                border-bottom: 1px solid #e5e5ea;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .three) td,
            .qtd-perf .two:not(:has(+ .two)):has(+ .four) td {
                padding-bottom: 0;
            }

            .qtd-perf .three:not(:has(+ .three)):has(+ .two) td {
                border-bottom: 1px solid #e5e5ea;
            }

            .qtd-perf .three td:nth-child(1) {
                padding-left: 13px;
            }

            .qtd-perf .two td,
            .qtd-perf .three td {
                border-bottom: none;
            }

            .qtd-perf .two:first-child td,
            .qtd-perf .three:first-child td,
            .qtd-perf .four:first-child td {
                border-bottom: 1px solid #e5e5ea;
            }

            .qtd-perf .two:has(+ .two) td {
                border-bottom: 1px solid #e5e5ea;
            }
            .qtd-perf.highlightTable tr.highlightBlue {
                background: #F7FBFE;
                font-weight: 500;
            }

            .footer {
                background-color: #f5f5f7;
                border-radius: 12px;
            }

            .footer-hint {
                color: #3a3a3c;
                font-size: 10px;
                line-height: 16px;
            }

            .footer-hint-text {
                color: #4F78E3;
                text-decoration: none;
            }

            .hint-text {
                color: #6e6e73;
                text-align: center;
                font-size: 10px;
                line-height: 14px;
                font-weight: 400;
            }
        }
    </style>
    <style>
        @media screen and (min-width:600px) {
            .tableWrapper{
                width:100%;
                max-width:880px !important;
            }
            .tableWrapper > table{
                padding:32px 40px !important;
            }
            .tableWrapper .header{
                padding:16px 30px !important;
            }
            .tableWrapper .header .icon{
                font-size:17px;
            }
            .tableWrapper .sub-title p:first-child{
                font-size:16px !important;
            }
            .type-btn-title p:first-child{
                font-size: 16px;
                font-weight: normal !important;
            }
            .type-btn-title p:last-child{
                font-size: 14px !important;
            }
            .wrap-box-div .timeTip{
                padding-top:6px;
                font-size:14px !important;
                line-height: 20px !important;
            }
            .tableTitle{
                padding:24px 0 12px 8px;
                line-height: 20px !important;
                font-size:16px !important;
                font-weight: normal !important;
            }
            .tableTitle span{
                font-size:16px;
            }
            .tableTitle div{
                font-size:16px;
            }
            .tableTitle .timeTip{
                font-size:14px;
                line-height: normal !important;
            }
            .footer-hint{
                font-size: 14px !important;
            }
            .hint-text{
                font-size: 12px !important;
            }
            .qtd-perf th{
                font-size:13px !important;
                padding:12px !important;
                line-height: 16px !important;
            }
            .qtd-perf .one td,.qtd-perf .two td  {
                font-size:14px !important;
                line-height: 16px !important;
                padding:16px 12px !important;
            }
            .qtd-perf .two td{
                font-weight: 400 !important;
                padding-bottom:12px !important;
            }
            .qtd-perf .three td{
                font-size:14px !important;
                padding:12px 12px 0 12px !important;
                line-height: 16px !important;
            }
            .qtd-perf .three td:nth-child(1){
                padding:12px 12px 0 20px !important;
            }
            .qtd-perf-padding{
                padding-top:16px !important;
            }
            .qtd-perf .two:not(:has(+ .two)):has(+ .three.pro) + tr td, .qtd-perf .three.highlightBlue.cons:not(:has(+ .cons)) + tr td{
                padding-top:6px !important;
            }
            .qtd-perf .highlightBlue td{
                padding:12px 12px 0 12px !important;
            }
            .qtd-perf .three.highlightBlue.pro td, .qtd-perf .three.highlightBlue.cons td{
                padding-top:6px !important;
                padding-bottom:6px !important;
                font-weight: 400 !important;
            }
            .qtd-perf .three:not(:has(+ .three)):not(:has(+ .four)) td{
                padding-bottom:12px !important;
            }
            .qtd-perf .two:not(:has(+ .two)):has(+ .three) td, .qtd-perf .two:not(:has(+ .two)):has(+ .four) td{
                padding-bottom:0 !important;
            }
            .qtd-perf .two:not(:has(+ .two)):has(+ .three.pro) td{
                padding-bottom:6px !important;
            }
        }
        @media screen and (max-width: 600px) {
            .widthFixed .first-tr th:first-child {
                width: 18%
            }
        }
    </style>
    <meta name="color-scheme" content="light dark">
    <meta name="supported-color-schemes" content="light dark">
</head>

<body style="word-spacing:normal;">
<div style="">
    <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="tableWrapper-outlook" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div class="tableWrapper" style="margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
            <tbody>
            <tr>
                <td style="direction:ltr;font-size:0px;padding:0;text-align:center;">
                    <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
                    <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                            <tbody>
                            <!-- 页眉区域 -->
                            <tr>
                                <td align="left" class="header" style="font-size:0px;padding:16px 24px;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="header-text">
                                            <div>
                                                <span class="icon"></span>
                                                <span class="title">Expert</span>
                                            </div>
                                            <div class="sub-title">
                                                <p style="font-size: 17px; font-weight: 600;">iPhone EOH Aging & EOH UB</p>
                                                <p>{{ send_date }}</p>
                                            </div>
                                        </div>
                                        <div class="apple-icon"></div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" style="font-size:0px;padding:0 2px 0 8px;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="wrap-box-div">
                                            <div class="type-btn-title">
                                                <p>iPhone EOH Aging & EOH UB</p>
                                                <p style="margin: 0">Updated to:{{ data_as_of_date }}</p>
                                            </div>
                                            <div class="timeTip">This page only tracks the Disti/T1 involved in the Direct Shipment Program.</div>
                                            {% if customized_data.get('tips') %}
                                                <div class="timeTip highlightRed noPadding">{{ customized_data.get('tips') }}</div>
                                                <!--
                                                {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == 'Multi Brand' or customized_data.get('rtm_version') == 'Mono Brand' %}
                                                    <div class="timeTip highlightRed noPadding">{{ customized_data.get('tips') }}</div>
                                                {% endif %}
                                                -->
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="tableTitle"><span>iPhone EOH Aging - Overall</span>
                                            {% if customized_data.get('rtm_version') == 'All' %}
                                            <div class="timeTip">Carrier EOH Aging accumulates from the 2024/09/13 landing, Education from the 2025/03/02 landing, and other RTMs from the 2024/08/18 landing.</div>
                                            {% elif customized_data.get('rtm_version') == 'Carrier' %}
                                            <div class="timeTip">EOH Aging starts to accumulate from 2024/09/13 landing.</div>
                                            {% elif customized_data.get('rtm_version') == 'Education' %}
                                            <div class="timeTip">EOH Aging starts to accumulate from 2025/03/02 landing.</div>
                                            {% else %}
                                            <div class="timeTip">EOH Aging starts to accumulate from 2024/08/18 landing.</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf widthFixed" style="font-size:0px;padding:0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                        <colgroup>
                                            <col>
                                            <col>
                                            <col>
                                            <col width="10%">
                                        </colgroup>
                                        <thead>
                                        <tr class="first-tr">
                                            <th rowspan="2"></th>
                                            <th rowspan="2">Accum.<br />Landing<br />@CDC</th>
                                            <th rowspan="2">Accum.<br />Landing<br />EOH</th>
                                            <th colspan="7">EOH Aging</th>
                                        </tr>
                                        <tr class="two-tr">
                                            <th>>5 wks</th>
                                            <th>5-6 wks</th>
                                            <th>6-7 wks</th>
                                            <th>7-8 wks</th>
                                            <th>8-9 wks</th>
                                            <th>9-10 wks</th>
                                            <th>>10 wks</th>
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('eoh_aging_overall') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                        <tr class="{{ row.level(customized_data.get('rtm_version')) }}">
                                            <td>
                                                {% if row.level() == 'two' %}
                                                -{{ row.first_column() }}
                                                {% else %}
                                                {{ row.first_column() }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_qtd) }}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_eoh) }} <div>{{ row.landing_eoh_percent() }}</div>
                                            </td>
                                            <td class="{{ row.age_gre35_threshold_class_name(row.age_gre35) }}">
                                                {{ row.convert_to_thousands(row.age_gre35) }} <div>{{ row.default_percent(row.age_gre35) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_5to6weeks) }} <div>{{ row.default_percent(row.age_5to6weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_6to7weeks) }} <div>{{ row.default_percent(row.age_6to7weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_7to8weeks) }} <div>{{ row.default_percent(row.age_7to8weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_8to9weeks) }} <div>{{ row.default_percent(row.age_8to9weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_9to10weeks) }} <div>{{ row.default_percent(row.age_9to10weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_gre70) }} <div>{{ row.default_percent(row.age_gre70) }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf widthFixed" style="font-size:0px;padding:12px 0 0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                        <colgroup>
                                            <col>
                                            <col>
                                            <col>
                                            <col width="10%">
                                        </colgroup>
                                        <thead>
                                        <tr class="first-tr">
                                            <th rowspan="2"></th>
                                            <th rowspan="2">Accum.<br />Landing<br />@CDC</th>
                                            <th rowspan="2">Accum.<br />Landing<br />EOH</th>
                                            <th colspan="6">EOH Aging</th>
                                        </tr>
                                        <tr class="two-tr">
                                            <th>≤5 wks</th>
                                            <th>≤1 wks</th>
                                            <th>1-2 wks</th>
                                            <th>2-3 wks</th>
                                            <th>3-4 wks</th>
                                            <th>4-5 wks</th>
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('eoh_aging_overall') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                        <tr class="{{ row.level(customized_data.get('rtm_version')) }}">
                                            <td>
                                                {% if row.level() == 'two' %}
                                                -{{ row.first_column() }}
                                                {% else %}
                                                {{ row.first_column() }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_qtd) }}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_eoh) }} <div>{{ row.landing_eoh_percent() }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_leq35) }} <div>{{ row.default_percent(row.age_leq35) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_leq1weeks) }} <div>{{ row.default_percent(row.age_leq1weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_1to2weeks) }} <div>{{ row.default_percent(row.age_1to2weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_2to3weeks) }} <div>{{ row.default_percent(row.age_2to3weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_3to4weeks) }} <div>{{ row.default_percent(row.age_3to4weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_4to5weeks) }} <div>{{ row.default_percent(row.age_4to5weeks) }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            {# Carrier取消该模块 #}
                            {% if customized_data.get('rtm_version') != 'Carrier' %}
                            <tr>
                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="tableTitle">
                                            <div>iPhone EOH Aging - Disti/T1 View</div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf widthFixed" style="font-size:0px;padding:0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                        <colgroup>
                                            <col>
                                            <col>
                                            <col>
                                            <col width="10%">
                                        </colgroup>
                                        <thead>
                                        <tr class="first-tr">
                                            <th rowspan="2"></th>
                                            <th rowspan="2">Accum.<br />Landing<br />@CDC</th>
                                            <th rowspan="2">Accum.<br />Landing<br />EOH</th>
                                            <th colspan="7">EOH Aging</th>
                                        </tr>
                                        <tr class="two-tr">
                                            <th>>5 wks</th>
                                            <th>5-6 wks</th>
                                            <th>6-7 wks</th>
                                            <th>7-8 wks</th>
                                            <th>8-9 wks</th>
                                            <th>9-10 wks</th>
                                            <th>>10 wks</th>
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('eoh_aging_nand') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                        <tr class="{{ row.level_by_nand() }}">
                                            <td>
                                                {% if row.level_by_nand() == 'two' %}
                                                -{{ row.first_column() }}
                                                {% else %}
                                                {{ row.first_column() }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_qtd) }}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_eoh) }} <div>{{ row.landing_eoh_percent() }}</div>
                                            </td>
                                            <td class="{{ row.age_gre35_threshold_class_name(row.age_gre35) }}">
                                                {{ row.convert_to_thousands(row.age_gre35) }} <div>{{ row.default_percent(row.age_gre35) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_5to6weeks) }} <div>{{ row.default_percent(row.age_5to6weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_6to7weeks) }} <div>{{ row.default_percent(row.age_6to7weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_7to8weeks) }} <div>{{ row.default_percent(row.age_7to8weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_8to9weeks) }} <div>{{ row.default_percent(row.age_8to9weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_9to10weeks) }} <div>{{ row.default_percent(row.age_9to10weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_gre70) }} <div>{{ row.default_percent(row.age_gre70) }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf widthFixed" style="font-size:0px;padding:12px 0 0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                        <colgroup>
                                            <col>
                                            <col>
                                            <col>
                                            <col width="10%">
                                        </colgroup>
                                        <thead>
                                        <tr class="first-tr">
                                            <th rowspan="2"></th>
                                            <th rowspan="2">Accum.<br />Landing<br />@CDC</th>
                                            <th rowspan="2">Accum.<br />Landing<br />EOH</th>
                                            <th colspan="6">EOH Aging</th>
                                        </tr>
                                        <tr class="two-tr">
                                            <th>≤5 wks</th>
                                            <th>≤1 wks</th>
                                            <th>1-2 wks</th>
                                            <th>2-3 wks</th>
                                            <th>3-4 wks</th>
                                            <th>4-5 wks</th>
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('eoh_aging_nand') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                        <tr class="{{ row.level_by_nand() }}">
                                            <td>
                                                {% if row.level_by_nand() == 'two' %}
                                                -{{ row.first_column() }}
                                                {% else %}
                                                {{ row.first_column() }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_qtd) }}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_eoh) }} <div>{{ row.landing_eoh_percent() }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_leq35) }} <div>{{ row.default_percent(row.age_leq35) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_leq1weeks) }} <div>{{ row.default_percent(row.age_leq1weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_1to2weeks) }} <div>{{ row.default_percent(row.age_1to2weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_2to3weeks) }} <div>{{ row.default_percent(row.age_2to3weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_3to4weeks) }} <div>{{ row.default_percent(row.age_3to4weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_4to5weeks) }} <div>{{ row.default_percent(row.age_4to5weeks) }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="tableTitle">
                                            <div>iPhone EOH Aging - Sub-LOB View</div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf highlightTable widthFixed" style="font-size:0px;padding:0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                        <colgroup>
                                            <col>
                                            <col>
                                            <col>
                                            <col width="10%">
                                        </colgroup>
                                        <thead>
                                        <tr class="first-tr">
                                            <th rowspan="2"></th>
                                            <th rowspan="2">Accum.<br />Landing<br />@CDC</th>
                                            <th rowspan="2">Accum.<br />Landing<br />EOH</th>
                                            <th colspan="7">EOH Aging</th>
                                        </tr>
                                        <tr class="two-tr">
                                            <th>>5 wks</th>
                                            <th>5-6 wks</th>
                                            <th>6-7 wks</th>
                                            <th>7-8 wks</th>
                                            <th>8-9 wks</th>
                                            <th>9-10 wks</th>
                                            <th>>10 wks</th>
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('eoh_aging_sublob') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                        {# sub_lob 16 需要高亮处理 #}
                                        {% set highLightClass = "" %}
                                        {% if row.first_column() == '16 Pro' %}
                                        {% set highLightClass = "highlightBlue pro" %}
                                        {% elif row.first_column() == '16 Cons.' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% elif row.first_column() == '16e' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% endif %}
                                        <tr class="{{ row.sublob_level() }} {{ highLightClass }}">
                                            <td>
                                                {% if row.sublob_level() == 'two' %}
                                                -{{ row.first_column() }}
                                                {% else %}
                                                {{ row.first_column() }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_qtd) }}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_eoh) }} <div>{{ row.landing_eoh_percent() }}</div>
                                            </td>
                                            <td class="{{ row.age_gre35_threshold_class_name(row.age_gre35) }}">
                                                {{ row.convert_to_thousands(row.age_gre35) }} <div>{{ row.default_percent(row.age_gre35) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_5to6weeks) }} <div>{{ row.default_percent(row.age_5to6weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_6to7weeks) }} <div>{{ row.default_percent(row.age_6to7weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_7to8weeks) }} <div>{{ row.default_percent(row.age_7to8weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_8to9weeks) }} <div>{{ row.default_percent(row.age_8to9weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_9to10weeks) }} <div>{{ row.default_percent(row.age_9to10weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_gre70) }} <div>{{ row.default_percent(row.age_gre70) }}</div>
                                            </td>s s
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf highlightTable widthFixed" style="font-size:0px;padding:12px 0 0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                        <colgroup>
                                            <col>
                                            <col>
                                            <col>
                                            <col width="10%">
                                        </colgroup>
                                        <thead>
                                        <tr class="first-tr">
                                            <th rowspan="2"></th>
                                            <th rowspan="2">Accum.<br />Landing<br />@CDC</th>
                                            <th rowspan="2">Accum.<br />Landing<br />EOH</th>
                                            <th colspan="6">EOH Aging</th>
                                        </tr>
                                        <tr class="two-tr">
                                            <th>≤5 wks</th>
                                            <th>≤1 wks</th>
                                            <th>1-2 wks</th>
                                            <th>2-3 wks</th>
                                            <th>3-4 wks</th>
                                            <th>4-5 wks</th>
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('eoh_aging_sublob') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                        {# sub_lob 16 需要高亮处理 #}
                                        {% set highLightClass = "" %}
                                        {% if row.first_column() == '16 Pro' %}
                                        {% set highLightClass = "highlightBlue pro" %}
                                        {% elif row.first_column() == '16 Cons.' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% elif row.first_column() == '16e' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% endif %}
                                        <tr class="{{ row.sublob_level() }} {{ highLightClass }}">
                                            <td>
                                                {% if row.sublob_level() == 'two' %}
                                                -{{ row.first_column() }}
                                                {% else %}
                                                {{ row.first_column() }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_qtd) }}
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.landing_eoh) }} <div>{{ row.landing_eoh_percent() }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_leq35) }} <div>{{ row.default_percent(row.age_leq35) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_leq1weeks) }} <div>{{ row.default_percent(row.age_leq1weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_1to2weeks) }} <div>{{ row.default_percent(row.age_1to2weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_2to3weeks) }} <div>{{ row.default_percent(row.age_2to3weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_3to4weeks) }} <div>{{ row.default_percent(row.age_3to4weeks) }}</div>
                                            </td>
                                            <td>
                                                {{ row.convert_to_thousands(row.age_4to5weeks) }} <div>{{ row.default_percent(row.age_4to5weeks) }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="tableTitle"><span>EOH UB - Overall</span>
                                            <div class="timeTip">This page tracks the UB devices during the fiscal week or quarter but in the warehouse.</div>
<!--                                            {% if customized_data.get('rtm_version') == 'All' %}-->
<!--                                            <div class="timeTip lightTip">FY24Q4: Carrier starts to accumulate from 2024/09/13 landing and other RTMs start to accumulate from 2024/08/18 landing.</div>-->
<!--                                            {% elif customized_data.get('rtm_version') == 'Carrier' %}-->
<!--                                            <div class="timeTip lightTip">FY24Q4: The indicator starts to accumulate from 2024/09/13 landing.</div>-->
<!--                                            {% else %}-->
<!--                                            <div class="timeTip lightTip">FY24Q4: The indicator starts to accumulate from 2024/08/18 landing.</div>-->
<!--                                            {% endif %}-->
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf ub-table" style="font-size:0px;padding:0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                        <thead>
                                        <tr class="first-tr">
                                            <th></th>
                                            <th>{{ customized_data.get('last_quarter') }}</th>
                                            <th>{{ customized_data.get('current_quarter') }}</th>
                                            {% for week in customized_data.get('rolling_weeks') %}
                                            <th>{{ week.quarter_name() }}{{ week.week_name() }}</th>
                                            {% endfor %}
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('over_all_ub_qtd') %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.get('rtm') %}
                                        <tr class="{{ row.get('level') }}">
                                            {% set index = loop.index-1 %}
                                            <td>
                                                {% if row.get('level') == 'two' %}
                                                -{{ row.get('field1') }}
                                                {% else %}
                                                {{ row.get('field1') }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.get('ub_without_ds_accum_lq', '-') }} <div>{{ row.get('otd_percentage_lq', '-') }}</div>
                                            </td>
                                            <td>
                                                {{ row.get('ub_without_ds_accum', '-') }} <div>{{ row.get('otd_percentage', '-') }}</div>
                                            </td>
                                            {% set over_all_ub_cw1 = customized_data.get('over_all_ub_cw1')[index] %}
                                            <td>
                                                {{ over_all_ub_cw1.get('ub_without_ds_weekly', '-') }} <div> {{  over_all_ub_cw1.get('percentage', '-') }}</div>
                                            </td>
                                            {% set over_all_ub_cw2 = customized_data.get('over_all_ub_cw2')[index] %}
                                            <td>
                                                {{ over_all_ub_cw2.get('ub_without_ds_weekly', '-') }} <div>{{  over_all_ub_cw2.get('percentage', '-') }}</div>
                                            </td>
                                            {% set over_all_ub_cw3 = customized_data.get('over_all_ub_cw3')[index] %}
                                            <td>
                                                {{ over_all_ub_cw3.get('ub_without_ds_weekly', '-') }} <div>{{  over_all_ub_cw3.get('percentage', '-') }}</div>
                                            </td>
                                            {% set over_all_ub_cw4 = customized_data.get('over_all_ub_cw4')[index] %}
                                            <td>
                                                {{ over_all_ub_cw4.get('ub_without_ds_weekly', '-') }}<div>{{  over_all_ub_cw4.get('percentage', '-') }}</div>
                                            </td>
                                            {% set over_all_ub_cw5 = customized_data.get('over_all_ub_cw5')[index] %}
                                            <td>
                                                {{ over_all_ub_cw5.get('ub_without_ds_weekly', '-') }} <div>{{  over_all_ub_cw5.get('percentage', '-') }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="tableTitle">EOH UB - Sub-LOB View</div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="qtd-perf ub-table highlightTable" style="font-size:0px;padding:0 0 24px;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                        <thead>
                                        <tr class="first-tr">
                                            <th></th>
                                            <th>{{ customized_data.get('last_quarter') }}</th>
                                            <th>{{ customized_data.get('current_quarter') }}</th>
                                            {% for week in customized_data.get('rolling_weeks') %}
                                            <th>{{ week.quarter_name() }}{{ week.week_name() }}</th>
                                            {% endfor %}
                                        </tr>
                                        </thead>
                                        {% for row in customized_data.get('sub_lob_ub_qtd') %}
                                        {% set index = loop.index-1 %}
                                        {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.get('rtm') %}
                                        {# 新插入四行数据 #}
                                        {% if index == 1 and customized_data.get('rtm_version') == 'All' %}
                                        {% for new_row in customized_data.get('region_sub_lob_ub_qtd') %}
                                        {% set highLightClass = "" %}
                                        {% if new_row.get('sub_lob') == '16 Pro' %}
                                        {% set highLightClass = "highlightBlue pro" %}
                                        {% elif new_row.get('sub_lob') == '16 Cons.' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% elif new_row.get('sub_lob') == '16e' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% endif %}
                                        <tr class="three {{ highLightClass }}">
                                            {% set new_index = loop.index-1 %}
                                            <td>
                                                {{ new_row.get('sub_lob') }}
                                            </td>
                                            <td>
                                                {{ new_row.get('ub_without_ds_accum_lq', '-') }} <div>{{ new_row.get('otd_percentage_lq', '-') }}</div>
                                            </td>
                                            <td>
                                                {{ new_row.get('ub_without_ds_accum', '-') }} <div>{{ new_row.get('otd_percentage', '-') }}</div>
                                            </td>
                                            {% set region_sub_lob_ub_cw1 = customized_data.get('region_sub_lob_ub_cw1')[new_index] %}
                                            <td>
                                                {{ region_sub_lob_ub_cw1.get('ub_without_ds_weekly', '-') }} <div> {{  region_sub_lob_ub_cw1.get('percentage', '-') }}</div>
                                            </td>
                                            {% set region_sub_lob_ub_cw2 = customized_data.get('region_sub_lob_ub_cw2')[new_index] %}
                                            <td>
                                                {{ region_sub_lob_ub_cw2.get('ub_without_ds_weekly', '-') }} <div> {{  region_sub_lob_ub_cw2.get('percentage', '-') }}</div>
                                            </td>
                                            {% set region_sub_lob_ub_cw3 = customized_data.get('region_sub_lob_ub_cw3')[new_index] %}
                                            <td>
                                                {{ region_sub_lob_ub_cw3.get('ub_without_ds_weekly', '-') }} <div> {{  region_sub_lob_ub_cw3.get('percentage', '-') }}</div>
                                            </td>
                                            {% set region_sub_lob_ub_cw4 = customized_data.get('region_sub_lob_ub_cw4')[new_index] %}
                                            <td>
                                                {{ region_sub_lob_ub_cw4.get('ub_without_ds_weekly', '-') }} <div> {{  region_sub_lob_ub_cw4.get('percentage', '-') }}</div>
                                            </td>
                                            {% set region_sub_lob_ub_cw5 = customized_data.get('region_sub_lob_ub_cw5')[new_index] %}
                                            <td>
                                                {{ region_sub_lob_ub_cw5.get('ub_without_ds_weekly', '-') }} <div> {{  region_sub_lob_ub_cw5.get('percentage', '-') }}</div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        {% endif %}
                                        {% set highLightClass = "" %}
                                        {% if row.get('field1') == '16 Pro' %}
                                        {% set highLightClass = "highlightBlue pro" %}
                                        {% elif row.get('field1') == '16 Cons.' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% elif row.get('field1') == '16e' %}
                                        {% set highLightClass = "highlightBlue cons" %}
                                        {% endif %}
                                        <tr class="{{ row.get('level') }} {{ highLightClass }}">
                                            <td>
                                                {% if row.get('level') == 'two' %}
                                                -{{ row.get('field1') }}
                                                {% else %}
                                                {{ row.get('field1') }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ row.get('ub_without_ds_accum_lq', '-') }} <div>{{ row.get('otd_percentage_lq', '-') }}</div>
                                            </td>
                                            <td>
                                                {{ row.get('ub_without_ds_accum', '-') }} <div>{{ row.get('otd_percentage', '-') }}</div>
                                            </td>
                                            {% set sub_lob_ub_cw1 = customized_data.get('sub_lob_ub_cw1')[index] %}
                                            <td>
                                                {{ sub_lob_ub_cw1.get('ub_without_ds_weekly', '-') }} <div> {{  sub_lob_ub_cw1.get('percentage', '-') }}</div>
                                            </td>
                                            {% set sub_lob_ub_cw2 = customized_data.get('sub_lob_ub_cw2')[index] %}
                                            <td>
                                                {{ sub_lob_ub_cw2.get('ub_without_ds_weekly', '-') }} <div>{{  sub_lob_ub_cw2.get('percentage', '-') }}</div>
                                            </td>
                                            {% set sub_lob_ub_cw3 = customized_data.get('sub_lob_ub_cw3')[index] %}
                                            <td>
                                                {{ sub_lob_ub_cw3.get('ub_without_ds_weekly', '-') }} <div>{{  sub_lob_ub_cw3.get('percentage', '-') }}</div>
                                            </td>
                                            {% set sub_lob_ub_cw4 = customized_data.get('sub_lob_ub_cw4')[index] %}
                                            <td>
                                                {{ sub_lob_ub_cw4.get('ub_without_ds_weekly', '-') }}<div>{{  sub_lob_ub_cw4.get('percentage', '-') }}</div>
                                            </td>
                                            {% set sub_lob_ub_cw5 = customized_data.get('sub_lob_ub_cw5')[index] %}
                                            <td>
                                                {{ sub_lob_ub_cw5.get('ub_without_ds_weekly', '-') }} <div>{{  sub_lob_ub_cw5.get('percentage', '-') }}</div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </table>
                                </td>
                            </tr>
                            <!-- 页脚区域 -->
                            <tr>
                                <td align="left" class="footer" style="font-size:0px;padding:18px;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="footer-hint">
                                            <div style="text-align:center">If you have any questions, please contact</div>
                                            <div style="text-align:center"><a href="mailto:<EMAIL>" class="footer-hint-text"><EMAIL></a>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" style="font-size:0px;padding:16px 0 0;word-break:break-word;">
                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                        <div class="hint-text"> For internal use only. Sent from Expert Technical Support. </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
</div>
</body>

</html>
