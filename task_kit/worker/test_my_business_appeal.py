# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############
from task_kit.worker.my_business_appeal.my_business_appeal_with_pos_excel_worker import \
    MyBusinessAppealWithPosExcelWorker
from task_kit.worker.my_business_appeal.my_business_appeal_worker import MyBusinessAppealWorker


# usage
# ENV=dev pytest -s test/test_esr.py

def test_mybiz_pos_suspension_abnormally():
    # MyBusinessAppealWorker(
    #     params='{"email_cmd":"mybiz_pos_suspension_abnormally_remind","fiscal_week":"FY25Q2W1","deadline":"2025-01-15 12:00:00","reseller_id":"265275","reseller_type":"T1"}'
    # ).do()

    # MyBusinessAppealWorker(
    #     params='{"email_cmd":"mybiz_pos_suspension_abnormally_remind","fiscal_week":"FY25Q2W3","deadline":"2025-01-18 20:00:00","reseller_id":"667139","reseller_type":"2"}'
    # ).do()

    MyBusinessAppealWorker(
        params='{"email_cmd":"mybiz_pos_suspension_abnormally_remind","fiscal_week":"FY25Q2W3","deadline":"2025-01-18 20:00:00","reseller_id":"893413","reseller_type":"1"}'
    ).do()




def test_mybiz_pos_suspension_appeal_window_about_to_close():
    MyBusinessAppealWorker(
        params='{"email_cmd":"mybiz_pos_suspension_appeal_window_about_to_close","fiscal_week":"FY25Q2W1","deadline":"2025-01-15 12:00:00","reseller_id":"265275","reseller_type":"T1","reseller_name":"北京英龙华辰科技有限公司","pos_name":"英龙华辰北京顺义店"}'
    ).do()



def test_my_business_appeal_with_pos_excel():
    params = '''{
            "email_cmd": "pos_suspension_abnormally_remind_with_pos_excel",
            "fiscal_week": "FY25Q4W2",
            "reseller_id": "1194595",
            "rtm": "Multibrand",
            "pos_list": "[{\\"pos_id\\":\\"1306674\\",\\"pos_name\\":\\"山河零售石家庄联盟路电信店\\"}]",
            "reseller_type": "2"
        }'''
    MyBusinessAppealWithPosExcelWorker(
        params=params
    ).do()
