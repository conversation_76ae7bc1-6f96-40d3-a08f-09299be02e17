import json
import traceback
from datetime import datetime
from typing import Optional

from data.mysqls.mybusiness_appeal.mybiz_reseller_secure import MybizResellerSecure
from kit.email.sender import Sender, CustomizedData

from util.conf import logger
from util.mail_conf import get_normal_system_link


class UbRadarWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error("no param configed of UbRadarWorker.")
            raise Exception("param is required")

        params = json.loads(params)

        self.params = params
        self.email_cmd = params.get("email_cmd")
        self.fiscal_week = params.get("fiscal_week")
        self.count = params.get("count")
        self.reseller_id = params.get("reseller_id")
        self.rtm = params.get("rtm")
        self.reseller_type = params.get("reseller_type")  # 1 就是总代/总公司 2就是其他的分公司 T1、T2
        self.params['jump_workspace_link'] = get_normal_system_link(host='https://gcmybusiness-dev.apple.com',
                                                                    system_link='radar',
                                                                    enter_from="source=external_email")

    def __get_reseller_email(self) -> Optional[list]:
        email_records = MybizResellerSecure.get_email_list_by_reseller_id(self.reseller_id)
        if not email_records:
            return None
        return [item.email for item in email_records]

    def do(self):
        try:
            now = datetime.now()
            reseller_emails = self.__get_reseller_email()
            if not reseller_emails:
                raise Exception(f"mybusiness::ub_radar, fiscal_week: {self.fiscal_week}, rtm: {self.rtm}, "
                                f"reseller_type:{self.reseller_type}, reseller_id: {self.reseller_id}查无对应邮箱.")
            sender = Sender(self.email_cmd, custom_format_subject=True, need_check_duplication=False)

            sender.set_recipients(recipient_list=reseller_emails)
            sender.send(customized_data=CustomizedData(now, self.params))
        except Exception as e:
            logger.error(f"error_info: {str(e)}, traceback: {traceback.format_exc()}")
            # logger.exception(e)
            raise e
