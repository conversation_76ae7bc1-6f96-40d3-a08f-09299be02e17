import json
from datetime import datetime
from typing import Optional

from data.mysqls.mybusiness_appeal.mybiz_reseller_secure import MybizResellerSecure
from kit.email.sender import Sender, CustomizedData
from util.conf import logger
from util.mail_conf import get_normal_system_link


class MyBusinessStockingPlanlWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error(f"no param configed of {self.__class__.__name__}.")
            raise Exception("param is required")

        params = json.loads(params)

        self.params = params
        self.email_cmd = params.get("email_cmd")
        self.fiscal_week = params.get("fiscal_week")
        self.deadline = params.get("deadline", "") # 提交反馈截止时间
        self.forecast_data_release_time = params.get("forecast_data_release_time", "") # 最终销量预测数据预计发布时间
        self.reseller_id = params.get("reseller_id")
        self.reseller_name_zh = params.get("reseller_name_zh")

        self.params['jump_workspace_link'] = get_normal_system_link(host='https://gcmybusiness.apple.com',
                                                                    system_link='abnormalOperation',
                                                                    enter_from="source=external_email")

    def __get_reseller_email(self) -> Optional[str]:
        email_records = MybizResellerSecure.get_email_by_reseller_id(self.reseller_id)
        if not email_records:
            return None
        return email_records.email

    def do(self):
        try:
            now = datetime.now()
            reseller_email = self.__get_reseller_email()
            if not reseller_email:
                raise Exception(f"fiscal_week: {self.fiscal_week}, reseller_name_zh:{self.reseller_name_zh}, "
                                f"reseller_id: {self.reseller_id}查无对应邮箱.")
            sender = Sender(self.email_cmd, custom_format_subject=True, need_check_duplication=False)
            sender.set_recipients(recipient_list=[reseller_email])
            sender.send(customized_data=CustomizedData(now, self.params))
        except Exception as e:
            logger.exception(e)
            raise e
