import json
import os
import traceback
from ast import literal_eval
from datetime import datetime
from typing import Optional

import pandas as pd

from data.mysqls.mybusiness_appeal.mybiz_reseller_secure import MybizResellerSecure
from kit.email.sender import Sender, CustomizedData
from util.conf import logger
from util.file_util import get_absolute_path


class MyBusinessAppealWithPosExcelWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error("no param configed of MyBusinessAppealWorker.")
            raise Exception("param is required")

        params = json.loads(params)

        self.params = params
        self.email_cmd = params.get("email_cmd")
        self.rtm = params.get("rtm")
        self.fiscal_week = params.get("fiscal_week")
        self.reseller_id = params.get("reseller_id")
        self.reseller_type = params.get("reseller_type")  # 1 就是总代/总公司 2就是其他的分公司 T1、T2
        self.pos_list_str = params.get("pos_list")
        self.attachment_file_path = '/uploads/pos_suspension_appeal'

    def __get_reseller_email(self) -> Optional[list]:
        email_records = MybizResellerSecure.get_email_list_by_reseller_id(self.reseller_id)
        if not email_records:
            return None
        return [item.email for item in email_records]

    def __get_pos_excel(self):
        # 安全地转换为Python对象
        data = literal_eval(self.pos_list_str)
        # 创建带日期的目录路径
        date_str = datetime.now().strftime("%Y-%m-%d")
        attachment_file_path = os.path.join('/uploads/pos_suspension_appeal', date_str)

        # 确保目录存在
        os.makedirs(get_absolute_path(attachment_file_path), exist_ok=True)

        # 创建DataFrame并导出Excel
        df = pd.DataFrame(data)

        file_path = f"/{self.fiscal_week}_{self.rtm + '_' if self.rtm else ''}_{self.reseller_id}_pos_suspension_appeal.xlsx"
        relative_file_path = attachment_file_path + file_path
        absolute_file_path = get_absolute_path(attachment_file_path) + file_path
        df.rename(columns={'pos_id': '停货Apple ID','pos_name': 'Apple Name'}, inplace=True)
        df.to_excel(absolute_file_path, index=False)
        return json.dumps([relative_file_path])

    def do(self):
        try:
            now = datetime.now()
            reseller_emails = self.__get_reseller_email()
            if not reseller_emails:
                raise Exception(f"fiscal_week: {self.fiscal_week}, reseller_type:{self.reseller_type}, "
                                f"reseller_id: {self.reseller_id}查无对应邮箱.")

            if not self.pos_list_str:
                raise Exception(f"fiscal_week: {self.fiscal_week}, reseller_type:{self.reseller_type}, "
                                f"reseller_id: {self.reseller_id}查无对应附件.")
            file_path = self.__get_pos_excel()

            sender = Sender(self.email_cmd, custom_format_subject=True, need_check_duplication=False, attachments=file_path)
            sender.set_recipients(recipient_list=reseller_emails)
            sender.send(customized_data=CustomizedData(now, self.params))
        except Exception as e:
            logger.error(f"error_info: {str(e)}, traceback: {traceback.format_exc()}")
            # logger.exception(e)
            raise e
