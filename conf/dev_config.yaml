
# Database credentials
secret_host: "gcdmp-eng.corp.apple.com"
secret_path: "/v2/aws/secrets/get" 

database:
  # Visit from Apple Network
  apple_host: "expert-rds-dev.corp.apple.com"
  # Visit from AWS Network
  aws_host: "expert-dev-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "app_fastlite_dev"
  secret_region: "us-west-2"
  default_db: "common_service"

cache:
  type: "simple"
  threshold: 20000000
  default_timeout: 2592000
  dir: ""

mail_config:
  secret_name: "expert-system-mail"
  secret_region: "us-west-2"
  smtp_host: "mail.apple.com"
  smtp_port: 587
  receivers: "<EMAIL>"
  cpf_publish_receivers: "<EMAIL>"
  cpf_sell_in_demand_publish_receivers: "<EMAIL>"

read_db:
  apple_host: "expert-database.corp.apple.com"
  aws_host: "expert-rds-cluster-expert-instance.cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "app_fastlite_prod"
  secret_region: "us-west-2"
  default_db: "common_service"

redis_config:
  apple_host: "dmp-redis-test.apple.com"
  aws_host: "master.dmp-dev-redis-rep-group.zmbvpy.usw2.cache.amazonaws.com"
  port: 6379
  secret_name: "dmp-dev-redis_auth_token"
  secret_region: "us-west-2"
  default_db: 0

gc_dmp_db:
  apple_host: "dmpdw-prod.corp.apple.com"
  aws_host: "dmpdw-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  apple_port: 3306 
  aws_port: 3306
  secret_name: "dmpdw-cluster-application_fast"
  secret_region: "us-west-2"
  default_db: "gc_dmp_fast"

gc_dmp_datasource:
  apple_host: "dmpdw-prod.corp.apple.com"
  aws_host: "dmpdw-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  apple_port: 3306
  aws_port: 3306
  secret_region: "us-west-2"
  datasource_db: "gc_dmp_datasource"
  datasource_secret: "dmpdw-application_datasource"

fast_lite:
  apple_host: "expert-rds-dev.corp.apple.com"
  aws_host: "expert-dev-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "app_fastlite_dev"
  secret_region: "us-west-2"
  default_db: "fast_lite"
  mono_db: "Allocation"
  channel_compliance: "channel_compliance"
  fast_npi_secure: "fast_npi_secure"

npi_secure:
  apple_host: "expert-rds-dev.corp.apple.com"
  aws_host: "expert-dev-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "rds-secret-expert-dev"
  secret_region: "us-west-2"
  default_db: "fast_npi_secure"
  fast_npi_secure: "fast_npi_secure"

navigate:
  app_id_key: "5ece29a97d7aa73bc0beaea58463960224bbb801b9717e0c45a9aa42708cf24a"
  apple_connect: "https://idmsac.corp.apple.com/IDMSWebAuth/login"
  new_apple_connect: "https://gcsales.expert-dev.apple.com"
  path: "/prep-allocation/detail"

gc_dmp_fast_write:
  apple_host: "dmpdw-prod.corp.apple.com"
  apple_port: 3306
  aws_host: "dmpdw-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  aws_port: 3306
  secret_name: "dmp-dw-application_fast_write"
  secret_region: "us-west-2"
  default_db: "gc_dmp_fast_write"

supply_data:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend-application_fast"
  secret_region: "us-west-2"
  default_db: "gc_dmp_fast"

dmp_dw_common:
  apple_host: "dmpdw-prod.corp.apple.com"
  aws_host: "dmpdw-cluster.cluster-ro-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "dmpdw-cluster-application_forecasting_tracking"
  secret_region: "us-west-2"
  default_db: "dmp_dw_common"

dfa_data:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend-application_fast"
  secret_region: "us-west-2"
  default_db: "gc_dmp_fast"

mono_allocation:
  apple_host: "expert-rds-dev.corp.apple.com"
  aws_host: "expert-dev-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "app_fastlite_dev"
  secret_region: "us-west-2"
  default_db: "Allocation"

databend_gc_dmp_fast_write:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "application_fast_databend_write"
  secret_region: "us-west-2"
  default_db: "gc_dmp_fast_write"

gc_dmp_directship:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend-application_email_report"
  secret_region: "us-west-2"
  default_db: "test_db"

gc_dmp_algo_ali_databend:
  apple_host: "dmp-databend-ali-general.corp.apple.com:443"
  aws_host: "************:8000"
  secret_name: "alidatabend-application_email_report"
  secret_region: "us-west-2"
  default_db: "gc_dmp_algo"

gc_dmp_systemusage:
  apple_host: "dmpdw-prod.corp.apple.com"
  aws_host: "dmpdw-cluster.cluster-ro-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "databend-application_system_usage"
  secret_region: "us-west-2"
  default_db: "gc_dmp_systemusage"

email_report:
  apple_host: "expert-database.corp.apple.com"
  aws_host: "expert-rds-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "app_fastlite_prod"
  secret_region: "us-west-2"
  default_db: "email_report"

gc_dmp_macroforecast:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend-application_macroforecast"
  secret_region: "us-west-2"
  default_db: "gc_dmp_macroforecast"

channel_compliance_databend:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend-application_email_report"
  secret_region: "us-west-2"
  default_db: "test_db"

gc_dmp_mystore_mybiz:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend_application_mybusiness"
  secret_region: "us-west-2"
  default_db: "gc_dmp_mystore_mybiz"

mybusiness_mail_config:
  secret_name: "mybusiness_system_email"
  secret_region: "us-west-2"
  smtp_host: "mail.apple.com"
  smtp_port: 587

mybusiness_secure:
  apple_host: "expert-rds-dev.corp.apple.com"
  aws_host: "expert-dev-cluster.cluster-cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  secret_name: "application_mybiz_secure_test"
  secret_region: "us-west-2"
  default_db: "mybusiness_secure"

mybusiness:
  apple_host: "mystore-rds-dev.corp.apple.com"
  apple_port: 3306
  aws_host: "mystore-dev.cw9p43cnirdp.us-west-2.rds.amazonaws.com"
  aws_port: 3306
  secret_name: "app_mybusiness_dev"
  secret_region: "us-west-2"
  default_db: "mybusiness"

ali_expert_host: "https://expert-dev.corp.apple.com"


smart_tower:
  https_host: "dmp-databend-http-eng.awusw2.shld.apple.com:443"
  secret_name: "databend-application_email_report"
  secret_region: "us-west-2"
  default_db: "test_db"
