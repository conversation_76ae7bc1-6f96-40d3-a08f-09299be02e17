from enum import Enum

STATUS_EXIST = 0
STATUS_DELETED = 1

class Ret:
    Code = 'code'
    Msg = 'msg'
    Data = 'data'

# error code and error message
class ErrCode:
    Success = 0
    System = 10000
    Param = 10001
    Permissions = 10011
    NoSoldToPermission = 10012
    DBInsert = 10020
    DBQueryParams = 10021
    DBQueryError = 10022
    DBQueryNoData = 10023
    DBConnectError = 10024
    FileUploadError = 10030
    ValidationError = 10040
    StatusError = 10050
    TemplateError = 10060
    UnknownError = 20000

    S3BucketNoContentInResponse = 30000
    TimeOutError = 40000

    NoSupplyFileInTargetWeek = 30001

    InsufficientStock = 50000

ErrMsg = {
    ErrCode.Success: "ok",
    ErrCode.Param: "params error. ",
    ErrCode.DBInsert: "insert to db error. ",
    ErrCode.System: "system error. ",
    ErrCode.Permissions: "permission restrictions",
    ErrCode.NoSoldToPermission: "You do not have the data permission of any sold-to. Please contact the DMP team for assistance. ",
    ErrCode.DBQueryNoData: "The chosen product has no data during this period. Please choose other products.",
    ErrCode.UnknownError: "Please try again",
}

class RtmType:
    Default = ''
    OTC = 'OTC'
    CES = 'CES'
    DutyFree = 'Duty-Free'
    Township = 'Township'
    JD = 'JD'
    LiveStreaming = 'Live Streaming'
    ChinaMobile = 'China Mobile'
    ChinaUnicom = 'China Unicom'
    ChinaTelecom = 'China Telecom'


class OperationRtm:
    Mono = 2
    Multi = 5
    Online = 6
    Carrier = 7


OperationRtmMap = {
    OperationRtm.Mono: [0, 1, 2, 14],
    OperationRtm.Multi: [0, 1, 5, 14],
    OperationRtm.Online: [0, 1, 6, 14],
    OperationRtm.Carrier: [0, 1, 7, 14]
}


RtmTypeRoleMap = {
    RtmType.Default: ['iPad', 'iPhone', 'Mac', 'Watch'],
    RtmType.OTC: ['OTC-Total', 'OTC-ASD', 'OTC-Telling', 'OTC-YY Authorized'],
    RtmType.CES: ['CES-Total', 'CES-IM ( excl. GM )', 'CES-CHJH / Costco / SM', 'CES-GM / SD / JR'],
    RtmType.DutyFree: ['DutyFree-Total', 'DutyFree-XTJ'],
    RtmType.Township: ['Township-Total', 'Township-JJYT', 'Township-YY Township'],
    RtmType.JD: ['JD-Total', 'JD-JD self-run'],
    RtmType.LiveStreaming: ['LiveStreaming-Total', 'LiveStreaming-Kuaishou', 'LiveStreaming-Douyin',
                            'LiveStreaming-WeChat'],
    RtmType.ChinaMobile: ['ChinaMobile-Total', 'ChinaMobile-North-1', 'ChinaMobile-North-2', 'ChinaMobile-East-1',
                          'ChinaMobile-East-2', 'ChinaMobile-South', 'ChinaMobile-West', 'ChinaMobile-Additional'],
    RtmType.ChinaUnicom: ['ChinaUnicom-Total', 'ChinaUnicom-North-1', 'ChinaUnicom-North-2', 'ChinaUnicom-East',
                          'ChinaUnicom-South', 'ChinaUnicom-West-1', 'ChinaUnicom-West-2', 'ChinaUnicom-Additional'],
    RtmType.ChinaTelecom: ['ChinaTelecom-Total', 'ChinaTelecom-North-1', 'ChinaTelecom-North-2', 'ChinaTelecom-East',
                           'ChinaTelecom-South', 'ChinaTelecom-West-1', 'ChinaTelecom-West-2',
                           'ChinaTelecom-Additional']
}


selectRange = ['-1', '-2']

ExpertPrsTypes = [1, 2, 3, 4, 6]

class BooleanStatus:
    false = 0
    true = 1

TimeoutWeek = 86400 * 7
TimeoutDay = 86400
TimeoutHour = 3600


OrderAsc = 0
OrderDesc = 1

DateTypeDay = 1
DateTypeWeek = 2

strYTD = 'YTD'
strQTD = 'QTD'
strWTD = 'WTD'
strLY = 'LY'
strLQ = 'LQ'
strLW = 'LW'

DateStrfDay = '%Y-%m-%d'

RedisCachePrefix = "fast.lite.cpf.server"
UrlPrefix = "/fast_lite_cpf"
UrlEndToEndPrefix = "/fast_end_to_end"
UrlPrefixUBVelocity = "/dashboard"
UrlPrefixdemand = "/demand"
UrlPrefixMono = "/mono"
UrlPrefixMacroForecast = "/macroforecast"


class ErrorExcept(BaseException):
    code: int
    err_msg: str

    def __init__(self, code, msg, *args: object) -> None:
        self.code = code
        self.err_msg = msg
        super().__init__(*args)

MODEL = [
    "iPhone 14 Pro Max",
    "iPhone 14 Pro",
    "iPhone 14 Plus",
    "iPhone 14",
    "iPhone 13",
    "iPhone 13 mini",
    "iPhone 12",
    "iPhone 11",
    "iPhone SE (3rd Gen)",
]

FULL_MODEL = [
    "iPhone 14 Pro Max",
    "iPhone 14 Pro",
    "iPhone 14 Plus",
    "iPhone 14",
    "iPhone 13 Pro Max",
    "iPhone 13 Pro",
    "iPhone 13",
    "iPhone 13 mini",
    "iPhone 12",
    "iPhone 11",
    "iPhone SE (3rd Gen)",
]

RTM = [
    "Lifestyle",
    "Mono",
    "Multi",
    "Channel Online",
    "Carrier",
    "ENT",
    "EDU",
]

CPF_BEGIN_WEEK = 202315
SHOW_MAX_WEEKS = 14

MONO_RTM = [
    "Lifestyle",
    "Mono"
]
FORECAST_FIRST_VERSION = 1
FORECAST_SECOND_VERSION = 2

DEMAND_FIRST_VERSION = 1
DEMAND_SECOND_VERSION = 2
DEMAND_THIRD_VERSION = 3

DownloadNational = 0
DownloadRTM = 1

TotalAndCurrentQuarterWeeks=["cq_total","cq_w1","cq_w2","cq_w3","cq_w4","cq_w5","cq_w6",
                             "cq_w7","cq_w8","cq_w9","cq_w10","cq_w11","cq_w12","cq_w13"]
SpecialWeek = ["cq_w14"]
NextQuarterFiveWeeks = ["nq_w1","nq_w2","nq_w3","nq_w4","nq_w5"]

StrENV = "ENV"
DOUBLE_LINE_FEED = "\n\n"

StrRTMCPF = "CP&F"
StrRTMCarrier = "Carrier"
StrRTMOnline = "Online"
StrRTMOnlineFull = "Channel Online"
StrRTMMulti = "Multi"
StrRoleExpert = "Expert"
StrRolePlanningTeam = "Planning Team"
FORECAST = "FORECAST"
DEMAND = "DEMAND"

MultiBusinessTypeOrder = [
    "OTC",
    "Township",
    "DF",
    "Mass Merchant"
]

CarrierSoldToOrder = [
    "CM",
    "CU",
    "CT"
]

OnlinePlatformOrder = [
    "JD self-run",
    "Banking",
    "Live-streaming",
    "SN/MN",
    "Marketplace",
    "Vertical"
]

ShieldPrsId = "Shield-Ds-Prsid"
ShieldPrsTypeCode = "Shield-Ds-prsTypeCode"
ShieldPrsFirstName = "Shield-Ds-firstName"
ShieldPrsLastName = "Shield-Ds-lastName"
ShieldPrsNickName = "Shield-Ds-nickName"
ShieldPrsEmail = "Shield-Ds-emailAddress"
ShieldPrsEmployeeId = "Shield-Ds-employeeId"
PrsIdKey = "prs_id"
SoldToNameKey = "sold_to_names"
SoldToIdKey = "sold_to_id"
UserTypeKey = "user_type"
RtmKey = "rtm"
ExternalViewBeginWeek = 202321
ExternalViewDemandVersion1Week = 202328


class UserType:
    Expert = 0
    External = 1


DateFormat = "%Y-%m-%d"
DateTimeFormat = "%Y-%m-%d %H:%M:%S"


class DataSourceFileType:
    Demand = "High&Low-runner for Demand"
    TargetTWOI = "High&Low-runner Target WOI"
    CarrierTargetTWOI = "Sold-to Target WOI"
    CPFSKUiPad = "Active SKU List-iPad"
    CPFSKUiPhone = "Active SKU List-iPhone"
    CPFSKUMac = "Active SKU List-Mac"
    CPFSKUAirPods = "Active SKU List-AirPods"
    CPFSKUWatch = "Active SKU List-Watch"
    CPFSKUAccessories = "Active SKU List-Accessories"
    CPFTWOSiPhone = "TWOS - iPhone"
    CPFSoldToMappingListIphone = "Sold-to Mapping List - iPhone"
    AllocationRunSupplyMix = "CPF Sold-to Mix - iPad"
    AllocationRunSupplyTWOS = "TWOS - iPad"
    SalesForecast = "Sales Forecast"


class AutomicJobType:
    SupplyData = "supply_data"


class EmailCmd:
    SupplyDataEmail = "supply_data_email"
    SupplyDataRecordEmail = "supply_data_record_email"
    WarningEmail = "warning_email"
    SupplyLostMpnEmail = "supply_lost_mpn_email"
    ESRRecordEmail = "esr_record_email"
    RTMFcstNewSoldtoEmail = "rtm_forcast_new_soldto_email"
    RTMFcstErrorEmail = "rtm_forcast_error_email"
    PosNppEmail = "pos_npp_email"
    RTMUploadSalesForecastReminderEmail = "rtm_upload_sales_forecast_reminder_email"
    Processor = "processor_email"
    Dashboard = "dashboard_email"
    Dashboard_V2 = "dashboard_email_v2"
    DashboardAlert = "dashboard_alert"
    EmailExecuteFailedAlert = "email_execute_failed_alert"
    EsrMainProductsEmail = "esr_main_products_email"
    EsrAccessoriesEmail = "esr_accessories_email"
    CTONotificationEmail = "cto_notification_email"
    MybusinessPosSuspensionAbnormallyRemind = "mybiz_pos_suspension_abnormally_remind"
    MybusinessPosSuspensionAppealWindowAboutToClose = "mybiz_pos_suspension_appeal_window_about_to_close"
    MybusinessUbRadar = "mybiz_reseller_ub_warning_notice"
    POS_SUSPENSION_ABNORMALLY_REMIND_WITH_POS_EXCEL = "pos_suspension_abnormally_remind_with_pos_excel"

class DataSourceFileStatus:
    Draft = 0
    Enabled = 1


class SkuType:
    iPhone = "sku_iphone"
    iPad = "sku_ipad"
    Mac = "sku_mac"
    Watch = "sku_watch"
    AirPods = "sku_airpods"
    Accessories = "sku_accessories"


class FileUploadError:
    MultiDemandTableHeadError = "The header in the file is invalid.\nThe header should be business_type, lob, sub_lob, sku, mpn_id, and label"
    MultiTwoiTableHeadError = "The header in the file is invalid.\nThe header should be business_type, lob, label, and twoi."
    CarrierTwoiTableHeadError = "The header in the file is invalid.\nThe header should be sold_to_id, sold_to_name, lob, and twoi."
    BusinessTypeError = "The invalid business_type: "
    SoldToIdError = "The invalid sold_to_id: "
    MPNOutOfScope = "The MPN not in the default Model scope: "
    MPNDuplicate = "The duplicated mpn_id: "
    MPNInvalid = "The invalid mpn_id: "
    WrongLabel = "Please use High-runner or Low-runner as the label."
    NoLabel = "The MPN with no label: "
    WrongLob = "The invalid lob: "
    TWOILessThanZero = "Please fill numbers that are equal or bigger than 0 in the twoi column."
    MultiDemandBlankData = "Please fill the blank columns (business_type, mpn_id, label) of the rows: "
    MultiTWOIBlankData = "Please fill the blank columns of the rows: "
    CarrierTWOIBlankData = "Please fill the blank columns (sold_to_id, lob, twoi) of the rows: "
    BusinessTypeMPNDuplicate = "The repeated rows: "
    UnknownError = "Please contact the DMP team for assistance."
    CPFSKUTableHeadError = "The header in the file is invalid.\n The header should be sales_org_id, sales_org, model, project_code, mpn_id, nand, color, odq, and hr_lr."
    SalesOrgIdError = "The invalid sales_org_id: "
    SalesOrgError = "The invalid sales_org: "
    ODQMoreThanZero = "Please fill integers that are bigger than 0 in the odq column."
    HRLRError = "Please use HR or LR in the hr_lr column."
    CPFMismatchError = "The rows sales_org_id and sale_org do not match: "
    RTMUploadHeadError = "The demand data file uploaded must be in the same format as the template."
    RTMUploadEmptyError = "Data in the file can not be empty."
    RTMUploadPreFilledError = "The pre-filled values in the file can not be modified."
    RTMUploadNewDataError = "New Data raws can not be added on the original template."
    RTMUploadRowsError = "The following rows are invalid: "
    RTMUploadQtyTooLargeError = "The total Sales Input Qty is too large: "
    SubmissionDuplicateField = "New Data raws can not be added on the original template."
    SubmissionDemandLowerThanSalesInput = "For each week, the number of “Top-up Demand” must be be greater than or equal to that of Sales Input submitted. Invalid rows: "
    SalesInputExceedOpenBacklogError = "The total quantity of Sales Input must not exceed the Open Backlog. Invalid rows: "
    CPFTWOSiPhoneHeaderError = "The very first row can not be modified, please re-use the data of the template."
    CPFTWOSiPhoneInvalidRowsPrefix = "The followings row(s) are invalid: "
    CPFTWOSiPhoneModelInvalidSuffix = ", please double check the product data (Model) mapping relationship."
    CPFTWOSiPhoneRTMBusinessTypeInvalidSuffix = ", please double check the customer data (RTM/ Business Type) mapping relationship."
    CPFTWOSiPhoneTWOSValueInvalidSuffix = " please note that any value in number format shall be kept to one decimal place and that negative values are not allowed."
    CPFTWOSiPhoneTypeInvalidSuffix = " because the value(s) of Type column is invalid."
    CPFTIdealDemandWeekDateInvalidSuffix = " the Week_Date data value shall be in the “FYxxQxxWxx” format, such as “FY23Q4W5”."
    CPFTIdealDemandMPNInvalidSuffix = ", please double check the product data (MPN) mapping relationship."
    CPFTIdealDemandSoldToIDInvalidSuffix = ", please double check the customer data (Sold-to ID) mapping relationship."
    CPFTIdealDemandCWXInvalidSuffix = ", please note that any value in number format shall be positive integer or zero, and the value can not be null."
    CPFTIdealDemandCPFXYInvalidSuffix = ", please note that any value in number format shall be kept to one decimal place and that the value can not be null."
    CPFTWOSiPhoneRTMBusinessTypeSoldtoInvalidSuffix = ", please double check the customer data (RTM/ Business Type /Customer Sold-to ID) mapping relationship."
    CPFTIdealDemandDuplicateInvalid = "There are duplicate data rows in the document. Please double check."
    CPFTSoldToMappingListHeaderError = "The very first row can not be modified, please keep it consistent with the  template."
    CPFTSoldToMappingListRowsPrefix = "The followings row(s) are invalid: "
    CPFTSoldToMappingListRowsRTMInvalidSuffix = " for wrong RTM name."
    CPFTSoldToMappingListRowsRTMFileTypeError = "Wrong format.  Please upload “.xlsx” format data file."
    CPFAllocationRunSupplyDataHeaderError = "The “Supply” data file uploaded must be in the same format as the original demand file."
    CPFAllocationRunSupplyDataRowColumnError = "Rows and columns in the Template can not be added or deleted."
    EmptyError = "Data in the file can not be empty."
    InvalidRowsError = "The following rows are invalid: "
    SpecialSupplyColumnsError = "The “Supply” data file uploaded must be in the same format as the original demand file."
    NppAllocationSupplyColumnsError = "The “Supply” data file uploaded must be in the same format as the original demand file."
    SuspensionReviewFileNotExistError = "File does not exist or file name is not correct. Please check the file name and try again."
    SuspensionReviewHeaderError = "Upload failed. The header of the uploaded file does not match the original file. Please adjust and resubmit."
    SuspensionReviewComplianceJudgeColumnError = 'Upload failed. For the field "Compliance Judge", uploaded values only support the following four: "Support"/"Reject"/ "support"/"reject". Please adjust and resubmit.'


DataSourceValidLob = [
    "iPhone",
    "iPad",
    "CPU",
    "Watch"
]

MultiHighLowRunnerForDemandColumns = [
    "business_type",
    "lob",
    "sub_lob",
    "sku",
    "mpn_id",
    "label",
]

MultiHighLowRunnerTWOIColumns = [
    "business_type",
    "lob",
    "label",
    "twoi",
]

CarrierHighLowRunnerTWOIColumns = [
    "sold_to_id",
    "sold_to_name",
    "lob",
    "twoi",
]

SpecialSoldToId = {}
CPFActiveSKUListiPadColumns = [
    "sales_org_id",
    "sales_org",
    "model",
    "project_code",
    "mpn_id",
    "nand",
    "color",
    "odq",
    "hr_lr"
]
CPFActiveSKUListiPhoneColumns = [
    "sales_org_id",
    "sales_org",
    "model",
    "project_code",
    "mpn_id",
    "nand",
    "color",
    "odq",
    "hr_lr",
    "rp_mpn_mapped_from_carrier",
    "tier",
    "series",
]
CPFSKUSalesOrgIdOrder = [
    1510,
    1200,
    1300
]
CPFSKUSalesOrgOrder = [
    "China mainland",
    "Hong Kong",
    "Taiwan"
]
CPFSKUSalesOrgMapping = {
    "China mainland": 1510,
    "Hong Kong":1200,
    "Taiwan": 1300
}

StrFiscalWeekYear = 'fiscal_week_year'
StrFiscalQtrWeekName = 'fiscal_qtr_week_name'

UnreadyDataError = "The data required for the allocation process has not been successfully captured yet."

TemplateHeaderDict = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "sold_to_name_en",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "CW Shipment Plan (Discrete)": "cw_shipment_plan",
    "Sni (Cw-1)": "sni_cw_minus_1",
    "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
    "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2",
    "EOH": "eoh",
    "St/Ub Cw-1": "st_ub_qty_cw_minus1",
    "ST/UB 5 Wk Bwd Avg": "st_ub_5wk_bwd_avg",
    "Open Backlog over Published for CW+3 SP": "open_backlog_over_published_sp_cw3",
    "Priority": "priority",
    "Sales input Qty for CW+1": "sales_input_qty_cw1",
    "Reason for CW+1": "reason_cw1",
    "Sales input Qty for CW+2": "sales_input_qty_cw2",
    "Reason for CW+2": "reason_cw2",
    "Sales input Qty for CW+3": "sales_input_qty_cw3",
    "Reason for CW+3": "reason_cw3",
    "Sales input Qty for CW+4": "sales_input_qty_cw4",
    "Reason for CW+4": "reason_cw4",
    "Comments": "comments"
}

TemplateFileRawHeader = list(TemplateHeaderDict.values())

TemplateFileHeader = list(TemplateHeaderDict.keys())

ESRMondayVersion = 1
ESRTuesdayVersion = 2
ESRWednesdayVersion = 3

class RTMAllocationPhase:
    SalesInput = 1
    DemandSubmission = 2
    DemandAdjustment = 3
    AllocationFeedback = 4

class CPFAllcationPhase:
    DemandCollection = 1
    AllocationRun = 2
    AllocationFeedback = 3

class CPFAllocationPhaseOneModule:
    SalesInput = 1
    SellinDemand = 2
    Adjustment = 3
    Overview = 4

P0ReasonValidate = [
    "Risk for lawsuit/penalty",
    "Risk for continuity of Apple’s business in the following quarter/year",
    "Deal",
    "Urgent demo(s)"
]
P1ReasonValidate = [
    "Heavy partner/Sales escalation",
    "Deal",
    "DG",
    "Customer back orders",
    "Demo(s)",
]
P2ReasonValidate = [
    "LOW WOI"
]

OverlapColumns = TemplateFileHeader[:21]

class RTMSalesInputUploadStatus:
    WaitingToUpload = 1
    Uploaded = 2
    Received = 3
    Completed = 4
    NeedToReupload = 5
    NotUpload = 6
    Uncompleted = 7
    UncompletedRerunRTM = 8
    UncompletedRerunCPF = 9

class RTMDemandSubmissionStatus:
    NotGenerated = 1
    InProcess = 2
    Completed = 3
    Error = 4

class AllocationRTM:
    Mono = 'Mono'
    Multi = 'Multi'
    Online = 'Online'
    Carrier = 'Carrier'
    ENT = 'ENT'
    EDU = 'EDU'
    HKTWCarrier = 'HK/TW Carrier'
    HKTWRP = 'HK/TW RP'
    HKTWEDU = 'HK/TW EDU'
    HKTWENT = 'HK/TW ENT'

    @classmethod
    def get_all_rtms(cls):
        """动态获取所有RTM类型值"""
        return [
            value for attr, value in cls.__dict__.items()
            if not attr.startswith('__') 
            and not callable(value)
            and not isinstance(value, classmethod)
        ]

AllocationRTMList = [
    'Mono',
    'Multi',
    'Online',
    'Carrier',
    'ENT',
    'EDU',
    'HK/TW Carrier',
    'HK/TW RP',
    'HK/TW EDU',
    'HK/TW ENT',
]

EMPTY_TABLE = 0


SubmissionExecuteLobs = ["iPad"]

PrepareSubmissionMonoHeaderDict = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "sold_to_name_en",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "CW Shipment Plan (Discrete)": "shipment_plan_cw",
    "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
    "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2",
    "CW+3 Shipment Plan (Discrete)": "shipment_plan_cw3",
    "CW Backlog Gap": "cw_backlog_gap",
    "Top Up Demand CW+1": "top_up_demand_cw1",
    "Top Up Demand CW+2": "top_up_demand_cw2",
    "Top Up Demand CW+3": "top_up_demand_cw3",
    "Top Up Demand CW+4": "top_up_demand_cw4",
}

PrepareSubmissionOtherHeaderDict = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "sold_to_name_en",
    "Open Backlog over Published for CW+3 SP": "open_backlog_over_published_sp_cw3",
}


class PrepareSubmissionFileCategory:
    Template = 0
    WithTags = 1
    HrOnly = 2
    GenerateAndUpload = 3


PrepareCollectionUploadsHeaderDict = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "customer_name",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "CW Shipment Plan (Discrete)": "cw_shipment_plan",
    "Sni (Cw-1)": "sni_cw_minus_1",
    "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
    "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2",
    "EOH": "eoh",
    "St/Ub Cw-1": "st_ub_qty_cw_minus1",
    "ST/UB 5 Wk Bwd Avg": "st_ub_5wk_bwd_avg",
    "Open Backlog over Published for CW+3 SP": "open_backlog_over_published_sp_cw3",
    "Priority": "priority",
    "Qty for CW+1": "sales_input_qty_cw1",
    "Reason for CW+1": "reason_cw1",
    "Qty for CW+2": "sales_input_qty_cw2",
    "Reason for CW+2": "reason_cw2",
    "Qty for CW+3": "sales_input_qty_cw3",
    "Reason for CW+3": "reason_cw3",
    "Qty for CW+4": "sales_input_qty_cw4",
    "Reason for CW+4": "reason_cw4",
    "Comments": "comments",
    "Delta for adjustment in CW+1": "delta_adjustment_cw1",
    "Reason for adjustment in CW+1": "reason_adjustment_cw1",
    "Delta for adjustment in CW+2": "delta_adjustment_cw2",
    "Reason for adjustment in CW+2": "reason_adjustment_cw2",
    "Delta for adjustment in CW+3": "delta_adjustment_cw3",
    "Reason for adjustment in CW+3": "reason_adjustment_cw3",
    "Delta for adjustment in CW+4": "delta_adjustment_cw4",
    "Reason for adjustment in CW+4": "reason_adjustment_cw4",
    "Comments for adjustment": "comments_adjustment"
}


PrepareCollectionWithTagsHeaderDict = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "customer_name",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "CW Shipment Plan (Discrete)": "cw_shipment_plan",
    "Sni (Cw-1)": "sni_cw_minus_1",
    "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
    "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2",
    "EOH": "eoh",
    "St/Ub Cw-1": "st_ub_qty_cw_minus1",
    "ST/UB 5 Wk Bwd Avg": "st_ub_5wk_bwd_avg",
    "Open Backlog over Published for CW+3 SP": "open_backlog_over_published_sp_cw3",
    "Priority": "priority",
    "Qty for CW+1": "sales_input_qty_cw1",
    "Reason for CW+1": "reason_cw1",
    "Qty for CW+2": "sales_input_qty_cw2",
    "Reason for CW+2": "reason_cw2",
    "Qty for CW+3": "sales_input_qty_cw3",
    "Reason for CW+3": "reason_cw3",
    "Qty for CW+4": "sales_input_qty_cw4",
    "Reason for CW+4": "reason_cw4",
    "Comments": "comments",
}

PrepareCollectionWithTagsHeaderList = list(PrepareCollectionWithTagsHeaderDict.keys())

class PrepareCollectionAdjustmentFileCategory:
    Template = 0
    RTM = 1
    CPF = 2
    WithTags = 3


class AllocationOperateCategory:
    Download = 0
    SalesInputRerun = 1
    SalesInputPublish = 2
    SellInDemandPublish = 3
    AdjustmentRerun = 4
    SellInDemandDownload = 5
    OverviewDownload = 6
    SellInDemandConfirm = 7
    SalesInputApprove = 8
    AdjustmentApprove = 9
    SalesInputErrorConfirm = 10
    SellInDemandCPFupload = 11

CPF_TWOS_IPHONE_TEMPLATE_HEADER = [
    'Model',
    'MPN',
    'RTM',
    'Business Type',
    'Sold-to ID',
    'Sold-to Name',
    'TWOS',
    'HR/LR'
]
CPF_SOLD_TO_MAPPING_LIST_TEMPLATE_HEADER = [
    'Region',
    'RTM',
    'Business Type',
    'Sold-to ID',
    'Name',
    'Abbre.',
    'Remark'
]

RTM_FORECAST_TEMPLATE_NAME = 'RTM_Forecast_Template.xlsx'
RTM_FORECAST_TEMPLATE_SHEET_NAME = 'Forecast_Data_Template'
RTM_FORECAST_TEMPLATE_DICT = {
    "Customer Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "LOB": "lob",
    "Model": "model",
    "MPN": "mpn",
    "CW": "forecast_cw",
    "CW+1": "forecast_cw1",
    "CW+2": "forecast_cw2",
    "CW+3": "forecast_cw3",
    "CW+4": "forecast_cw4",
    "CW+5": "forecast_cw5",
    "CW+6": "forecast_cw6",
    "CW+7": "forecast_cw7",
    "CW+8": "forecast_cw8",
    "CW+9": "forecast_cw9",
    "Type": "forecast_type"
}
RTM_FORECAST_TEMPLATE_HEADER = list(RTM_FORECAST_TEMPLATE_DICT.keys())
RTM_FORECAST_TEMPLATE_HEADER_RAW = list(RTM_FORECAST_TEMPLATE_DICT.values())

IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_NAME = 'iPhone_TWOS_Adjustment(X)_Template.xlsx'
IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_SHEET_NAME = 'iPhone TWOS Adjustment(X)'

IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_DICT = {
    "Model": "model",
    "RTM": "rtm",
    "Business Type": "business_type",
    "Customer Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "X in CW+1": "x_in_cw1",
    "X in CW+2": "x_in_cw2",
    "X in CW+3": "x_in_cw3",
    "X in CW+4": "x_in_cw4",
    "Type": "forecast_type",
}
IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER = list(IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_DICT.keys())
IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER_RAW = list(IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_DICT.values())

IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_NAME = 'iPhone_TWOS_Adjustment(Y)_Template.xlsx'
IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_SHEET_NAME = 'iPhone TWOS Adjustment(Y)'
IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_DICT = {
    "Model": "model",
    "RTM": "rtm",
    "Business Type": "business_type",
    "Y in CW+1": "y_in_cw1",
    "Y in CW+2": "y_in_cw2",
    "Y in CW+3": "y_in_cw3",
    "Y in CW+4": "y_in_cw4",
    "Type": "forecast_type"
}
IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER = list(IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_DICT.keys())
IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER_RAW = list(IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_DICT.values())

RTM_TYPE_MAPPING = {
    "Mono": ["SO"],
    "ENT": ["UB"],
    "EDU": ["UB"],
    "Multi": ["UB"],
    "Carrier": ["UB"],
    "Online": ["SO"],
}

class IdealDemandRTMCategory:
    Template = 0
    Uploaded = 1
    FinalForecast = 2
    IdealDemand = 3
    
class IdealDemandCPFCategory:
    XTemplate = 0
    YTemplate = 1
    XAdjustment = 2
    YAdjustment = 3
    FinalForecast = 4
    IdealDemand = 5
    MonoSoEoh = 6
    OnlineSoEoh = 7


IDEAL_DEMAND_FINAL_FORECAST_DICT = {
    "Week_Date": "week_date",
    "Business Type": "business_type",
    "Customer Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "LOB": "lob",
    "Model": "model",
    "FPH4": "fph4",
    "Project Code": "project_code",
    "SKU": "sku",
    "MPN": "mpn",
    "CW": "forecast_cw",
    "CW+1": "forecast_cw1",
    "CW+2": "forecast_cw2",
    "CW+3": "forecast_cw3",
    "CW+4": "forecast_cw4",
    "CW+5": "forecast_cw5",
    "CW+6": "forecast_cw6",
    "CW+7": "forecast_cw7",
    "CW+8": "forecast_cw8",
    "CW+9": "forecast_cw9",
}

IDEAL_DEMAND_FINAL_FORECAST_HEADER = list(IDEAL_DEMAND_FINAL_FORECAST_DICT.keys())
IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW = list(IDEAL_DEMAND_FINAL_FORECAST_DICT.values())
IDEAL_DEMAND_FINAL_FORECAST_SHEET_NAME = 'Final Forecast Data'

DEFAULT_SHEET_NAME = 'Sheet1'

CPF_IDEAL_DEMAND_FINAL_FORECAST_DICT = {
    "Week_Date": "week_date",
    "RTM": "rtm",
    "Business Type": "business_type",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "LOB": "lob",
    "Model": "model",
    "Project Code": "project_code",
    "MPN": "mpn",
    "Nand": "nand",
    "Color": "color",
    "Fiscal Year": "fiscal_year",
    "Fiscal_Quarter": "fiscal_quarter",
    "Fiscal_Week": "fiscal_week",
    "AI Fcst": "ai_fcst",
    "RTM Fcst": "rtm_fcst",
    "Final Fcst": "final_fcst",
}

CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER = list(CPF_IDEAL_DEMAND_FINAL_FORECAST_DICT.keys())
CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW = list(CPF_IDEAL_DEMAND_FINAL_FORECAST_DICT.values())
CPF_IDEAL_DEMAND_FINAL_FORECAST_SHEET_NAME = 'CP&F Final Forecast'


CPF_SO_EOH_DICT = {
    "Fiscal Year": "fiscal_year",
    "Fiscal_Quarter": "fiscal_quarter",
    "Fiscal_Week": "fiscal_week_year",
    "RTM": "rtm",
    "Business Type": "business_type",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name_en",
    "LOB": "lob",
    "Model": "model",
    "Project Code": "project_code",
    "MPN": "mpn",
    "Nand": "nand",
    "Color": "color",
    "SO EOH": "so_eoh",
}
CPF_SO_EOH_HEADER = list(CPF_SO_EOH_DICT.keys())
CPF_SO_EOH_HEADER_RAW = list(CPF_SO_EOH_DICT.values())
CPF_SO_EOH_SHEET_NAME= 'SO_EOH'


CPF_IDEAL_DEMAND_DICT = {
    "Week_Date": "fiscal_qtr_week_name",
    "RTM": "rtm",
    "Business Type": "business_type",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name_en",
    "LOB": "lob",
    "Model": "model",
    "FPH4": "fph4",
    "Project Code": "project_code",
    "MPN": "mpn",
    "Nand": "nand",
    "Color": "color",
    "Inventory EOH": "inv_eoh",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "Shipment Plan CW": "shipment_plan_cw",
    "Shipment Plan CW+1": "shipment_plan_cw_1",
    "Shipment Plan CW+2": "shipment_plan_cw_2",
    "Shipment Plan CW+3": "shipment_plan_cw_3",
    "Shipment Plan CW+4": "shipment_plan_cw_4",
    "Top up Demand CW+1": "top_up_demand_cw_1",
    "Top up Demand CW+2": "top_up_demand_cw_2",
    "Top up Demand CW+3": "top_up_demand_cw_3",
    "Top up Demand CW+4": "top_up_demand_cw_4",
    "New Shipment Plan CW+1": "new_shipment_plan_cw_1",
    "New Shipment Plan CW+2": "new_shipment_plan_cw_2",
    "New Shipment Plan CW+3": "new_shipment_plan_cw_3",
    "New Shipment Plan CW+4": "new_shipment_plan_cw_4",
}
CPF_IDEAL_DEMAND_HEADER = list(CPF_IDEAL_DEMAND_DICT.keys())
CPF_IDEAL_DEMAND_HEADER_RAW = list(CPF_IDEAL_DEMAND_DICT.values())
CPF_IDEAL_DEMAND_SHEET_NAME= 'Sheet1'

RTM_IDEAL_DEMAND_DICT = {
    "Fiscal_Week_Year": "fiscal_week_year",
    "Week_Date": "fiscal_qtr_week_name",
    "RTM": "rtm",
    "Business Type": "business_type",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name_en",
    "LOB": "lob",
    "Model": "model",
    "fph4": "fph4",
    "Project Code": "project_code",
    "MPN": "mpn",
    "Nand": "nand",
    "Color": "color",
    "Inventory_EOH": "inv_eoh",
    "Top up Demand CW+1": "top_up_demand_cw_1",
    "Top up Demand CW+2": "top_up_demand_cw_2",
    "Top up Demand CW+3": "top_up_demand_cw_3",
    "Top up Demand CW+4": "top_up_demand_cw_4",
}
RTM_IDEAL_DEMAND_HEADER = list(RTM_IDEAL_DEMAND_DICT.keys())
RTM_IDEAL_DEMAND_HEADER_RAW = list(RTM_IDEAL_DEMAND_DICT.values())


ALLOCATION_RUN_SUPPLY_DATA_TEMPLATE = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "fph1",
    "Prod / FPH L3": "fph3",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "sold_to_name",
    "Max_CW_Shipment_Plan_and_Consumption_CQ_CW": "max_cw_shipment_plan_consumption_cq_cw",
    "Shipment_Plan_Solver_Landed_CW": "shipment_plan_solver_landed_cw",
    "Shipment_Plan_Solver_Landed_CW1": "shipment_plan_solver_landed_cw1",
    "Shipment_Plan_Solver_Landed_CW2": "shipment_plan_solver_landed_cw2",
    "Shipment_Plan_Solver_Landed_CW3": "shipment_plan_solver_landed_cw3",
    "Shipment_Plan_Solver_Landed_CW4": "shipment_plan_solver_landed_cw4",
    "Last_Week_Shipment_Plan_CW": "last_week_shipment_plan_cw",
    "Last_Week_Shipment_Plan_CW1": "last_week_shipment_plan_cw1",
    "Last_Week_Shipment_Plan_CW2": "last_week_shipment_plan_cw2",
    "Last_Week_Shipment_Plan_CW3": "last_week_shipment_plan_cw3",
    "Last_Week_Shipment_Plan_CW4": "last_week_shipment_plan_cw4",
    "Last_week_POR_discrete_CW1": "last_week_por_discrete_cw1",
    "Last_week_POR_discrete_CW2": "last_week_por_discrete_cw2",
    "Last_week_POR_discrete_CW3": "last_week_por_discrete_cw3",
    "Last_week_POR_discrete_CW4": "last_week_por_discrete_cw4",
}
ALLOCATION_RUN_SUPPLY_DATA_HEADER = list(ALLOCATION_RUN_SUPPLY_DATA_TEMPLATE.keys())
ALLOCATION_RUN_SUPPLY_DATA_HEADER_RAW = list(ALLOCATION_RUN_SUPPLY_DATA_TEMPLATE.values())

ALLOCATION_RUN_EXCESS_SUPPLY_DATA_TEMPLATE = {
    "LOB / FPH L1": "fph1",
    "Prod / FPH L3": "fph3",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Excess Supply CW+1": "excess_supply_cw1",
    "Excess Supply CW+2": "excess_supply_cw2",
    "Excess Supply CW+3": "excess_supply_cw3",
    "Excess Supply CW+4": "excess_supply_cw4",
}
ALLOCATION_RUN_EXCESS_SUPPLY_DATA_HEADER = list(ALLOCATION_RUN_EXCESS_SUPPLY_DATA_TEMPLATE.keys())
ALLOCATION_RUN_EXCESS_SUPPLY_DATA_HEADER_RAW = list(ALLOCATION_RUN_EXCESS_SUPPLY_DATA_TEMPLATE.values())

ALLOCATION_RUN_STOP_SUPPLY_DATA_TEMPLATE = {
    "Customer Sold-to ID": "sold_to_id",
    "Project Code": "project_code",
    "Start Week": "start_week",
    "End Week": "end_week",
}
ALLOCATION_RUN_STOP_SUPPLY_DATA_HEADER = list(ALLOCATION_RUN_STOP_SUPPLY_DATA_TEMPLATE.keys())
ALLOCATION_RUN_STOP_SUPPLY_DATA_HEADER_RAW = list(ALLOCATION_RUN_STOP_SUPPLY_DATA_TEMPLATE.values())

class AllocationRunProcessType:
    Automatic = 0
    Manual = 1

class AllocationRunStep:
    SupplyAcquisition = 1
    SupplyProtection = 2
    SpecialSupply = 3
    TrueIncrementalAllocation = 4
    ManualAdjustment = 5

class AllocationRunSupplyDataType:
    SupplyData = 0
    ExcessSupply = 1
    StopSupply = 2

class AllocationRunFileType:
    Template = 0
    Upload = 1

class SupplyDataUploadStatus:
    Unupload = 0
    Uploaded = 1
    Deleted = 2

class AllocationRunCalculateStatus:
    NotCalculated = 0
    InCalculation = 1
    CalculationSuccessful = 2
    CalculationFailed = 3


SUPPLY_CALCULATE_ERROR = "The 4 weeks’ Shipment Plan and the Total Incremental Supply cannot cover the Pull In Qty."

POR_CWX_COLUMNS = [
    "Last_week_POR_discrete_CW1",
    "Last_week_POR_discrete_CW2",
    "Last_week_POR_discrete_CW3",
    "Last_week_POR_discrete_CW4",
]

NEED_PROTECT_CW_PULL_IN_COLUMNS = [
    "Need_Protect_CW1_CW_Pull_in",
    "Need_Protect_CW2_CW_Pull_in",
    "Need_Protect_CW3_CW_Pull_in",
    "Need_Protect_CW4_CW_Pull_in",
]

NEED_PROTECT_CW_COLUMNS = [
    "Need_Protect_CW1",
    "Need_Protect_CW2",
    "Need_Protect_CW3",
    "Need_Protect_CW4",
]

INCREMENTAL_CWX_COLUMNS = [
    "Incremental CW+1",
    "Incremental CW+2",
    "Incremental CW+3",
    "Incremental CW+4",
]

TRUE_INCREMENTAL_CWX_COLUMNS = [
    "true Incremental CW+1",
    "true Incremental CW+2",
    "true Incremental CW+3",
    "true Incremental CW+4",
]

EXCESS_SYPPLY_CWX_COLUMNS = [
    "Excess Supply CW+1",
    "Excess Supply CW+2",
    "Excess Supply CW+3",
    "Excess Supply CW+4",
]

DataSourceIpadSoldToMixColumns = [
    "Sales Org",
    "RTML4",
    "LOB / FPH L1",
    "Prod / FPH L3",
    "Project Code",
    "Nand",
    "Color",
    "MPN / Apple Part #",
    "ODQ",
    "Customer Sold-to ID",
    "Customer Sold-to Name",
    "Mix"
]

DataSourceIpadTWOSColumns = [
    "Sales Org",
    "RTML4",
    "LOB / FPH L1",
    "Prod / FPH L3",
    "Project Code",
    "Nand",
    "Color",
    "MPN / Apple Part #",
    "ODQ",
    "Customer Sold-to ID",
    "Customer Sold-to Name",
    "TWOS",
    "Type"
]

AllocationRunSpecialSupplyColumns = [
    "Sales Org",
    "RTML4",
    "LOB / FPH L1",
    "Prod / FPH L3",
    "Project Code",
    "Nand",
    "Color",
    "MPN / Apple Part #",
    "ODQ",
    "Customer Sold-to ID",
    "Customer Sold-to Name",
    "Special Supply CW+1",
    "Special Supply CW+2",
    "Special Supply CW+3",
    "Special Supply CW+4"
]

AllocationRunSpecialSupplyDataColumns = [
    "sales_org",
    "rtml4",
    "fph1",
    "fph3",
    "project_code",
    "nand",
    "color",
    "mpn",
    "odq",
    "sold_to_id",
    "sold_to_name",
    "special_supply_cw1",
    "special_supply_cw2",
    "special_supply_cw3",
    "special_supply_cw4",
]

NppAllocationSupplyColumns = [
    "RTM",
    "Sales Org",
    "Customer Name",
    "Customer Sold To ID",
    "LOB",
    "Model",
    "Project Short Desc",
    "Revenue/Demo",
    "Apple Part #",
    "Lifecycle",
    "Description",
    "CW Supply Qty"
]

SupplyProtectionStrategy = ["Based on Forward WOI", "Based on Backward WOI", "Defult", "Sold-to Mix"]

FISCAL_WEEK_SEVERAL = 13
FISCAL_WEEKS_LENGTH = 52
QUARTER_3_FISCAL_WEEKS_LENGTH = 39

ALL = "All"
CARRIER = "Carrier"
RETAIL_PARTNER = "Retail Partner"
EDU = "EDU"
ENT = "ENT"
Education = "Education"
Enterprise = "Enterprise"
RP = "RP"
REGION_TAIWAN = "Taiwan"
REGION_HK = "Hong Kong"
REGION_CM = "China mainland"
LOB = "iPhone"
CPF = "CP&F"

MACRO_FORECAST_TOP_REPORT = "全局文案"
MACRO_FORECAST_UB_QUARTERLY_REPORT = "Quarterly UB Forecast"
MACRO_FORECAST_UB_ROLLING_REPORT = "Rolling 4 Quarters UB Forecast"
MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT = "Quarterly Smartphone Market Forecast"
MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT = "Rolling 4 Quarters Smartphone Market Forecast"

FAST_EMAIL_SUFFIX = 'fast_email_'
StrIphone = 'iPhone'

CARRIER_SUB_RTM = ['CM', 'CU', 'CT', 'CB']


class WOITypes(Enum):
    FinalDemand = "final_demand"
    FeedbackDemand = "feedback_demand"
