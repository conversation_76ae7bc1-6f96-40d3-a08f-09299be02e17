import os
import sys

from apis.allocation import npi_allocation
from apis.common import on_or_off, file
from apis.dashboard.channel_compliance import pos_ub_tracking
from apis.demand import ideal_demand, homepage, final_demand
from apis.end_to_end import ml_fcst_quantile, reseller_feedback_setting, demand_feedback
from apis.mono import mono_pos_allocation, pos_allocation

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from apis.api_common import server
from apis import ping, common_redis, forecast_api, demand_api, multi_api, online_api, carrier_api, user_api, \
    allocation_prepare_api, allocation_cpf_api, cpf_sell_in_demand_api, macroforecast_api
from apis import datasource_api, allocation_submission_api, task_api, allocation_collection_adjustment_api
from apis import rtm_demand_adjustment_api, cpf_overview_api
from apis import ideal_demand_api
from apis import allocation_run_api
from apis import allocation_run_preview_api
from apis.dashboard import ub_velocity, mpn_mix, demand, po_delinquent, po_gap, demand_comparison
from apis.demand import demand as demand_ideal
from apis.demand import dfa_upload
from apis.dashboard import forecast_comparison, forecast_actual
from apis import anti_fraud_so_ub_api
from apis import scan_image_api
from apis.datasource.automatic import esr
from apis.end_to_end.rtm_view import demand_forecast_feedback
from apis.suspension.appeal import handle_file
from apis.end_to_end.rtm_view import demand_forecast_feedback

# 注册路由
server.register_blueprint(ping.bp)
server.register_blueprint(common_redis.bp)
server.register_blueprint(forecast_api.bp)
server.register_blueprint(demand_api.bp)
server.register_blueprint(multi_api.bp)
server.register_blueprint(online_api.bp)
server.register_blueprint(carrier_api.bp)
server.register_blueprint(user_api.bp)
server.register_blueprint(datasource_api.bp)
server.register_blueprint(allocation_prepare_api.bp)
server.register_blueprint(allocation_cpf_api.bp)
server.register_blueprint(allocation_submission_api.bp)
server.register_blueprint(allocation_collection_adjustment_api.bp)
server.register_blueprint(cpf_sell_in_demand_api.bp)
server.register_blueprint(task_api.bp)
server.register_blueprint(rtm_demand_adjustment_api.bp)
server.register_blueprint(cpf_overview_api.bp)
server.register_blueprint(ideal_demand_api.bp)
server.register_blueprint(allocation_run_api.bp)
server.register_blueprint(allocation_run_preview_api.bp)
server.register_blueprint(ub_velocity.bp)
server.register_blueprint(mpn_mix.bp)
server.register_blueprint(demand.bp)
server.register_blueprint(on_or_off.bp)
server.register_blueprint(ideal_demand.bp)
server.register_blueprint(final_demand.bp)
server.register_blueprint(demand_ideal.bp)
server.register_blueprint(homepage.bp)
server.register_blueprint(dfa_upload.bp)
server.register_blueprint(po_delinquent.bp)
server.register_blueprint(forecast_comparison.bp)
server.register_blueprint(forecast_actual.bp)
server.register_blueprint(po_gap.bp)
server.register_blueprint(mono_pos_allocation.bp)
server.register_blueprint(demand_comparison.bp)
server.register_blueprint(anti_fraud_so_ub_api.bp)
# server.register_blueprint(macroforecast_api.bp)
server.register_blueprint(file.bp)
server.register_blueprint(pos_allocation.bp)
server.register_blueprint(scan_image_api.bp)
server.register_blueprint(esr.bp)
server.register_blueprint(pos_ub_tracking.bp)
server.register_blueprint(handle_file.bp)
server.register_blueprint(ml_fcst_quantile.bp)
server.register_blueprint(reseller_feedback_setting.bp)
server.register_blueprint(demand_forecast_feedback.bp)
server.register_blueprint(demand_feedback.bp)
server.register_blueprint(npi_allocation.bp)

if __name__ == "__main__":
    server.run(host='0.0.0.0', port=9090)
