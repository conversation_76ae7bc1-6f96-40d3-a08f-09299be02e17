import ast
import traceback
from concurrent.futures.thread import Thread<PERSON><PERSON><PERSON>xecutor
from hashlib import md5
import collections
from itertools import groupby

from celery_config import celery
from data.databend.esr.app_gbi_table_sync_status_di import AppGbiTableSyncStatusDi
from data.datasource_automatic_update_record import DataSourceFileRecordRepository,SYSTEM_OPERATOR
from data.model_sku_data import DimFastModelS<PERSON><PERSON><PERSON><PERSON><PERSON>, DimFastiPadSKUList, DimFastLobSKUList
from data.datasource_data import *
from data.multi_external_data import *
from data.carrier_external_data import *
from data.databend.vw_status_data import CpfVwStatusDatabend
from data.cpf_data_source import *
from data.allocation_esr_data import *
from data.allocation_prepare_gc_dmp_data import AppFastTaskStatusWi
from data.dmp_dw_common_data import DimPubProdInfo
from data.ideal_demand_data import DimIdealRtmSoldtoMappingA
from data.mysqls.automatic.automatic_file_info import AutomaticFileInfo
from domain.datasource.impl.automatic import AutomaticDataType, Automatic_DB_NAME, CTOName, KeyTableSelector, KeySubKeyRelation, \
    EsrName, KEY_TABLE_NAME_MAPPING, KEY_ESR_NAME_MAPPING
from domain.supply.entity import Lob
from domain.suspension.entity.const import SUSPENSION_APPEAL_UPLOAD_TYPE
from kit.save_as_file import save_as_zip
from kit.validator.file import raise_upload_error
from util.send_email import async_send_email_by_database, send_email_by_database


def columns_equals(origin, target):
    if not isinstance(origin, collections.Collection):
        raise ErrorExcept(ErrCode.System, "column name valid error")
    if not isinstance(target, collections.Collection):
        raise ErrorExcept(ErrCode.System, "column name valid error")
    if len(origin) != len(target):
        return False
    for i in range(len(origin)):
        if origin[i] != target[i]:
            return False
    return True


def columns_equals_lob(origin, target):
    if not isinstance(origin, collections.Collection):
        raise ErrorExcept(ErrCode.System, "column name valid error")
    if not isinstance(target, collections.Collection):
        raise ErrorExcept(ErrCode.System, "column name valid error")
    # 判断target是否包含origin
    if not set(origin).issubset(set(target)):
        return False
    return True


def file_save_multi_demand(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    # table header validate
    if not columns_equals(MultiHighLowRunnerForDemandColumns, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.MultiDemandTableHeadError)
    # business type validate
    invalid_bts = set()
    for bt in df["business_type"]:
        if bt not in MultiBusinessTypeOrder:
            invalid_bts.add(str(bt))
    if len(invalid_bts) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.BusinessTypeError + ", ".join(invalid_bts))
    # mpn validate
    all_mpn = DimFastModelSkuMapList.get_mpn_list_by_models(MODEL)
    invalid_mpn = set()
    for mpn in df["mpn_id"]:
        if mpn not in all_mpn:
            invalid_mpn.add(str(mpn))
    # if len(invalid_mpn) != 0:
        # raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.MPNInvalid + ", ".join(invalid_mpn))
    # label validate
    invalid_label = []
    for label in df["label"]:
        if label not in ["High-runner", "Low-runner"]:
            invalid_label.append(label)
    if len(invalid_label) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.WrongLabel)
    # data validate
    none_rows = df[df[["business_type", "mpn_id", "label"]].isnull().T.any()]
    if len(none_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.MultiDemandBlankData + ", ".join([str(x+1) for x in none_rows.T.columns]))
    duplicated = []
    for bt in MultiBusinessTypeOrder:
        bdf = df[df["business_type"] == bt]
        duplicated.extend(bdf[bdf["mpn_id"].duplicated()].T.columns)
    if len(duplicated) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.BusinessTypeMPNDuplicate + ", ".join(str(x+1) for x in duplicated))
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMMulti
    file_info.datasource_type = DataSourceFileType.Demand
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMMulti, DataSourceFileType.Demand)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMMulti, DataSourceFileType.Demand, today)
    if today_count == 0:
        file_info.filename = f"{DataSourceFileType.Demand}_{today}.xlsx"
    else:
        file_info.filename = f"{DataSourceFileType.Demand}_{today} {today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    # file process
    old_datas = OdsFastMultiDemand.query_by_version(version)
    if len(old_datas) != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    datas = []
    for index, row in df.iterrows():
        datas.append({
            "business_type": row["business_type"],
            "lob": row["lob"],
            "sub_lob": row["sub_lob"],
            "sku": row["sku"],
            "mpn_id": row["mpn_id"],
            "label": row["label"],
            "version": version,
        })
    OdsFastMultiDemand.batch_save(datas)
    file_info.save()
    return {"file_id": file_md5}


def file_save_multi_twoi(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    # table header validate
    if not columns_equals(MultiHighLowRunnerTWOIColumns, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.MultiTwoiTableHeadError)
    # business type validate
    invalid_bts = set()
    for bt in df["business_type"]:
        if bt not in MultiBusinessTypeOrder:
            invalid_bts.add(str(bt))
    if len(invalid_bts) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.BusinessTypeError + ", ".join(invalid_bts))
    # lob validate
    invalid_lobs = set()
    for lob in df["lob"]:
        if lob not in DataSourceValidLob:
            invalid_lobs.add(str(lob))
    if len(invalid_lobs) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.WrongLob + ", ".join(invalid_lobs))
    # label validate
    invalid_label = []
    for label in df["label"]:
        if label not in ["High-runner", "Low-runner"]:
            invalid_label.append(str(label))
    if len(invalid_label) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.WrongLabel)
    # twoi validate
    for twoi in df["twoi"]:
        if type(twoi) not in [int, float] or twoi < 0:
            raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.TWOILessThanZero)
    # data validate
    none_rows = df[df.isnull().T.any()]
    if len(none_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.MultiTWOIBlankData + ", ".join([str(x+1) for x in none_rows.T.columns]))
    duplicated = []
    for bt in MultiBusinessTypeOrder:
        for lob in DataSourceValidLob:
            bdf = df[df["business_type"] == bt][df["lob"] == lob]
            duplicated.extend(bdf[bdf["label"].duplicated()].T.columns)
    if len(duplicated) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.BusinessTypeMPNDuplicate + ", ".join(str(x+1) for x in duplicated))
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMMulti
    file_info.datasource_type = DataSourceFileType.TargetTWOI
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMMulti, DataSourceFileType.TargetTWOI)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMMulti, DataSourceFileType.TargetTWOI, today)
    if today_count == 0:
        file_info.filename = f"{DataSourceFileType.TargetTWOI}_{today}.xlsx"
    else:
        file_info.filename = f"{DataSourceFileType.TargetTWOI}_{today} {today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    # file processing
    old_datas = OdsFastMultiTargetTwoi.query_by_version(version)
    if len(old_datas) != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    datas = []
    for index, row in df.iterrows():
        datas.append({
            "business_type": row["business_type"],
            "lob": row["lob"],
            "label": row["label"],
            "twoi": row["twoi"],
            "version": version,
        })
    OdsFastMultiTargetTwoi.batch_save(datas)
    file_info.save()
    return {"file_id": file_md5}


def file_save_carrier_twoi(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    # table header validate
    if not columns_equals(CarrierHighLowRunnerTWOIColumns, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CarrierTwoiTableHeadError)
    # business type validate
    invalid_bts = set()
    for sold_to_id in df["sold_to_id"]:
        if str(sold_to_id) not in ["670060", "749106", "863735"]:
            invalid_bts.add(str(sold_to_id))
    if len(invalid_bts) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.SoldToIdError + ", ".join(invalid_bts))
    # lob validate
    invalid_lobs = set()
    for lob in df["lob"]:
        if lob not in DataSourceValidLob:
            invalid_lobs.add(str(lob))
    if len(invalid_lobs) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.WrongLob + ", ".join(invalid_lobs))
    # twoi validate
    for twoi in df["twoi"]:
        if type(twoi) not in [int, float] or twoi < 0:
            raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.TWOILessThanZero)
    # data validate
    none_rows = df[df[["sold_to_id", "lob", "twoi"]].isnull().T.any()]
    if len(none_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.CarrierTWOIBlankData + ", ".join([str(x+1) for x in none_rows.T.columns]))
    duplicated = []
    for bt in ["670060", "749106", "863735", 670060, 749106, 863735]:
        bdf = df[df["sold_to_id"] == bt]
        duplicated.extend(bdf[bdf["lob"].duplicated()].T.columns)
    if len(duplicated) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.BusinessTypeMPNDuplicate + ", ".join(str(x+1) for x in duplicated))
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCarrier
    file_info.datasource_type = DataSourceFileType.CarrierTargetTWOI
    file_info.filename = file.filename
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCarrier, DataSourceFileType.CarrierTargetTWOI)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCarrier, DataSourceFileType.CarrierTargetTWOI,
                                                              today)
    if today_count == 0:
        file_info.filename = f"{DataSourceFileType.CarrierTargetTWOI}_{today}.xlsx"
    else:
        file_info.filename = f"{DataSourceFileType.CarrierTargetTWOI}_{today} {today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    # file processing
    old_datas = OdsFastCarrierTargetTwoi.query_by_version(version)
    if len(old_datas) != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    datas = []
    for index, row in df.iterrows():
        datas.append({
            "sold_to_id": row["sold_to_id"],
            "sold_to_name": row["sold_to_name"],
            "lob": row["lob"],
            "twoi": row["twoi"],
            "version": version,
        })
    OdsFastCarrierTargetTwoi.batch_save(datas)
    file_info.save()
    return {"file_id": file_md5}

def cpf_validate_data(df: pd.DataFrame):
    # table header validate
    if not columns_equals(CPFActiveSKUListiPadColumns, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFSKUTableHeadError)
    # sales_org_id validate
    invalid_sois = set()
    column_sales_org_id = df["sales_org_id"]
    for soi in column_sales_org_id:
        if soi not in CPFSKUSalesOrgIdOrder:
            invalid_sois.add(str(soi))
    if len(invalid_sois) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.SalesOrgIdError + ", ".join(invalid_sois))
    # sales_org validate
    invalid_sos = set()
    column_sales_org = df["sales_org"]
    for so in column_sales_org:
        if so not in CPFSKUSalesOrgOrder:
            invalid_sos.add(str(so))
    if len(invalid_sos) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.SalesOrgError + ", ".join(invalid_sos))    
    
    # mpn_id validate
    all_mpn = DimFastiPadSKUList.get_mpn_list_by_lob('iPad')
    invalid_mpn = set()
    for mpn in df["mpn_id"]:
        if mpn not in all_mpn:
            invalid_mpn.add(str(mpn))
    # if len(invalid_mpn) != 0:
    #     raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.MPNInvalid + ", ".join(invalid_mpn))
    
    # odq validate
    invalid_sos = set()
    for so in df["odq"]:
        if so <= 0:
            raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.ODQMoreThanZero)

    # hr_lr validate
    invalid_hr_lr = []
    for h_l in df["hr_lr"]:
        if h_l not in ["HR", "LR"]:
            invalid_hr_lr.append(h_l)
    if len(invalid_hr_lr) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.HRLRError)

    # data validate
    # columns mismatch
    mapped_column_sales_org_id = column_sales_org.map(CPFSKUSalesOrgMapping)
    mismatched_rows = df[mapped_column_sales_org_id != column_sales_org_id]
    if len(mismatched_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.CPFMismatchError + ", ".join([str(x+1) for x in mismatched_rows.T.columns]))

    # columns duplicate
    duplicated = df.duplicated(subset=['mpn_id'], keep=False)
    if duplicated.any():
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.BusinessTypeMPNDuplicate + ", ".join(str(x+1) for x in df[duplicated].T.columns))


def cpf_validate_data_lob(df: pd.DataFrame, lob):
    # table header validate
    valid_headers = CPFActiveSKUListiPadColumns
    header_error_message = FileUploadError.CPFSKUTableHeadError
    if lob == Lob.IPHONE.value:
        valid_headers = CPFActiveSKUListiPhoneColumns
        header_error_message = f"The header in the file is invalid.\n The header should be {', '.join(valid_headers)}"
    if not columns_equals_lob(valid_headers, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, header_error_message)
    # sales_org validate
    invalid_sos = set()
    column_sales_org = df["sales_org"]
    for so in column_sales_org:
        if so not in CPFSKUSalesOrgOrder:
            invalid_sos.add(str(so))
    if len(invalid_sos) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.SalesOrgError + ", ".join(invalid_sos))

    # mpn_id validate
    all_mpn = DimFastLobSKUList.get_mpn_list_by_lob(lob)
    invalid_mpn = set()
    for mpn in df["mpn_id"]:
        if mpn not in all_mpn:
            invalid_mpn.add(str(mpn))
    # if len(invalid_mpn) != 0:
    #     raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.MPNInvalid + ", ".join(invalid_mpn))
    # columns duplicate
    duplicated = df.duplicated(subset=['mpn_id', 'sales_org'], keep=False)
    if duplicated.any():
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.BusinessTypeMPNDuplicate + ", ".join(str(x+2) for x in df[duplicated].T.columns))


def cfp_save_data_source(df: pd.DataFrame, version: int, upload_by: str, upload_date: str):
    old_datas_count = OdsFastCPFActiveSKUiPad.query_by_version(version)
    if old_datas_count != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    week_info = FiscalYearWeek.get_week_by_date(upload_date)
    if len(week_info) == 0:
        raise ErrorExcept(ErrCode.System, "get week info error.")
    
    datas = []
    # 上传的文件中可以有空值，需要转化为None才可在数据库中保存
    df.replace({np.nan: None}, inplace=True)
    for _, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": week_info['fiscal_qtr_week_name'],
            "fiscal_week_year": week_info['fiscal_week_year'],
            "upload_by": upload_by,
            "upload_at": datetime.now(),
            "sales_org_id": row["sales_org_id"],
            "sales_org": row["sales_org"],
            "model": row["model"],
            "project_code": row["project_code"],
            "mpn_id": row["mpn_id"],
            "nand": row["nand"],
            "color": row["color"],
            "odq": row["odq"],
            "hr_lr": row["hr_lr"],
            "version": version,
        })
    OdsFastCPFActiveSKUiPad.batch_save(datas)


def cfp_save_data_source_by_lob(df: pd.DataFrame, version: int, upload_by: str, upload_date: str, lob: str):
    old_datas_count = OdsFastCPFActiveSKULob.query_by_version(version, lob)
    if old_datas_count != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    week_info = FiscalYearWeek.get_week_by_date(upload_date)
    if len(week_info) == 0:
        raise ErrorExcept(ErrCode.System, "get week info error.")

    datas = []
    # 上传的文件中可以有空值，需要转化为None才可在数据库中保存
    df.replace({np.nan: None}, inplace=True)
    for _, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": week_info['fiscal_qtr_week_name'],
            "fiscal_week_year": week_info['fiscal_week_year'],
            "upload_by": upload_by,
            "upload_at": datetime.now(),
            "sales_org_id": row["sales_org_id"],
            "sales_org": row["sales_org"],
            "lob": lob,
            "model": row["model"],
            "project_code": row["project_code"],
            "mpn_id": row["mpn_id"],
            "nand": row["nand"],
            "color": row["color"],
            "odq": row["odq"],
            "hr_lr": row["hr_lr"],
            "rp_mpn_mapped_from_carrier": row.get("rp_mpn_mapped_from_carrier", None),
            "tier": row.get("tier", None),
            "series": row.get("series", None),
            "version": version,
        })
    OdsFastCPFActiveSKULob.batch_save(datas)

def file_save_cpf_sku(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    
    cpf_validate_data(df)
    
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCPF
    file_info.datasource_type = DataSourceFileType.CPFSKUiPad
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCPF, DataSourceFileType.CPFSKUiPad)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCPF, DataSourceFileType.CPFSKUiPad, today)
    if today_count == 0:
        file_info.filename = f"{DataSourceFileType.CPFSKUiPad}_{today}.xlsx"
    else:
        file_info.filename = f"{DataSourceFileType.CPFSKUiPad}_{today} {today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    cfp_save_data_source(df, version, uploader, today)
    try :
        cfp_save_data_source_by_lob(df, version, uploader, today, Lob.IPAD.value)
    except Exception as e:
        logger.error(traceback.format_exc())
    except ErrorExcept as e:
        logger.error(traceback.format_exc())
    file_info.save()
    return {"file_id": file_md5}


def file_save_cpf_sku_lob(file, uploader, sku_lob):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    lob = SkuLobs.get(sku_lob)
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')

    cpf_validate_data_lob(df, lob)

    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCPF
    # 通过lob获取SkuTypes中的datasource_type

    file_info.datasource_type = SkuTypes.get(lob)

    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCPF, file_info.datasource_type)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCPF, file_info.datasource_type, today)
    if today_count == 0:
        file_info.filename = f"{file_info.datasource_type}_{today}.xlsx"
    else:
        file_info.filename = f"{file_info.datasource_type}_{today} {today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    cfp_save_data_source_by_lob(df, version, uploader, today, lob)
    file_info.save()
    return {"file_id": file_md5}


SkuLobs = {
    SkuType.iPad: Lob.IPAD.value,
    SkuType.Mac: Lob.MAC.value,
    SkuType.AirPods: Lob.AIRPODS.value,
    SkuType.iPhone: Lob.IPHONE.value,
    SkuType.Watch: Lob.WATCH.value,
    SkuType.Accessories: Lob.ACCESSORIES.value,
}

SkuTypes = {
    Lob.IPAD.value: DataSourceFileType.CPFSKUiPad,
    Lob.IPHONE.value: DataSourceFileType.CPFSKUiPhone,
    Lob.WATCH.value: DataSourceFileType.CPFSKUWatch,
    Lob.MAC.value: DataSourceFileType.CPFSKUMac,
    Lob.AIRPODS.value: DataSourceFileType.CPFSKUAirPods,
    Lob.ACCESSORIES.value: DataSourceFileType.CPFSKUAccessories,
}

SkuDataSourceType = {
    SkuType.iPad: DataSourceFileType.CPFSKUiPad,
    SkuType.iPhone: DataSourceFileType.CPFSKUiPhone,
    SkuType.Watch: DataSourceFileType.CPFSKUWatch,
    SkuType.Mac: DataSourceFileType.CPFSKUMac,
    SkuType.AirPods: DataSourceFileType.CPFSKUAirPods,
    SkuType.Accessories: DataSourceFileType.CPFSKUAccessories,
}


def file_get(file_id):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/{file_id}.xlsx'
    if not os.path.isfile(path):
        raise ErrorExcept(ErrCode.DBQueryNoData, "no this file")
    return path


def get_datasource_list(rtm):
    res = DataSourceList.query_manual_list(rtm)
    return [{
        "name": x.datasource_type,
        "type": x.simple,
        "last_upload_date": x.last_upload_date and x.last_upload_date.strftime(DateTimeFormat),
        "last_upload_by": x.last_upload_by,
    } for x in res]


def get_datasource_file_list(rtm, datasource_type):
    res = DataSourceFile.query_list_by_rtm(rtm, datasource_type)
    return [{
        "filename": x.filename,
        "upload_by": x.upload_by,
        "upload_date": x.create_date and x.create_date.strftime(DateTimeFormat),
        "url": x.url,
        "content_type": x.content_type,
    } for x in res]


def get_datasource_file_list_by_sku_lob(rtm, sku_lob):
    datasource_type = SkuDataSourceType.get(sku_lob)

    res = DataSourceFile.query_list_by_rtm(rtm, datasource_type)
    return [{
        "filename": x.filename,
        "upload_by": x.upload_by,
        "upload_date": x.update_date and x.update_date.strftime(DateTimeFormat),
        "url": x.url,
        "content_type": x.content_type,
    } for x in res]


def get_datasource_automatic_esr_list(channel=None):
    
    # res = AppFastESRMonWi.get_esr_list()
    # 更换ESR列表查询方式
    # key 对应的表
    rets = []
    # esr_main_table_name = "gc_ro_ds_esr_data"
    # esr_main_res = AutomaticFileInfo.query_records_exist(data_type=AutomaticDataType.ESR.value,
    #                                                      db_name=Automatic_DB_NAME,
    #                                                      table_name=esr_main_table_name,
    #                                                      rtm_view=channel)
    # esr_main_ret = {
    #     "key": KeySubKeyRelation.ESR_MAIN_PRODUCTS.value,
    #     "name": EsrName.ESR_MAIN_PRODUCTS.value,
    #     "last_update_time": esr_main_res.snapshot_ts.strftime('%Y-%m-%d %H:%M:%S') if esr_main_res else None
    # }

    # esr_acc_table_name = "gc_ro_ds_esr_accy_data"
    # esr_acc_res = AutomaticFileInfo.query_records_exist(data_type=AutomaticDataType.ESR.value,
    #                                                     db_name=Automatic_DB_NAME,
    #                                                     table_name=esr_acc_table_name,
    #                                                     rtm_view=channel)
    # esr_acc_ret = {
    #     "key": KeySubKeyRelation.ESR_ACCESSORIES.value,
    #     "name": EsrName.ESR_ACCESSORIES.value,
    #     "last_update_time": esr_acc_res.snapshot_ts.strftime('%Y-%m-%d %H:%M:%S') if esr_acc_res else None
    # }

    ret_group = []
    # 1、查询分好组的数据
    group_list = AutomaticFileInfo.query_group_records()
    # 2、查询对应权限的group
    for group in group_list:
        if channel == group.rtm_view:
            ret_group.append(group)
    list_ret = []
    # 3、按照data_type, table_name顺序组织数据结构
    table_mapping = {
        "gc_ro_ds_esr_data": {
            "key": KeySubKeyRelation.ESR_MAIN_PRODUCTS.value,
            "name": EsrName.ESR_MAIN_PRODUCTS.value,
        },
        "gc_ro_ds_esr_accy_data": {
            "key": KeySubKeyRelation.ESR_ACCESSORIES.value,
            "name": EsrName.ESR_ACCESSORIES.value,
        },
        "gc_dmp_pod": {
            "key": KeySubKeyRelation.CTO_POD.value,
            "name": CTOName.CTO_POD,
        },
        "gc_dmp_backlog": {
            "key": KeySubKeyRelation.CTO_BACKLOG.value,
            "name": CTOName.CTO_BACKLOG,
        },
        "gc_dmp_execution": {
            "key": KeySubKeyRelation.CTO_EXECUTION.value,
            "name": CTOName.CTO_EXECTION,
        }
    }
    for i in ret_group:
        # Ecosystem 仅有配件的权限看
        if channel and channel == "Ecosystem" and i.table_name != "gc_ro_ds_esr_accy_data":
            continue

        item_dict = table_mapping.get(i.table_name)
        item_dict.update(
           {"last_update_time": i.sync_time.strftime('%Y-%m-%d %H:%M:%S') if i.sync_time else None}
        )
        list_ret.append(item_dict)

    # 4、排序
    list_order = [EsrName.ESR_MAIN_PRODUCTS.value, EsrName.ESR_ACCESSORIES.value,
                  CTOName.CTO_POD, CTOName.CTO_BACKLOG, CTOName.CTO_EXECTION]
    try:
        rets = sorted(list_ret, key=lambda x: list_order.index(x["name"]))
    except Exception as e:
        logger.error(f"sort list error: {e}")

    return rets


def get_datasource_automatic_list():
    res = DataSourceFileRecordRepository.query_last_record_by_cmd(AutomicJobType.SupplyData)
    # 将res转成dict
    res_dict = {}
    for item in res:
        if item.lob not in res_dict:
            res_dict[item.lob] = []
        res_dict[item.lob].append(item[1])

    lobs = Lob.all()
    ret = []
    for lob in lobs:
        ret.append({
            "name": 'Supply Data-' + lob,
            "type": "supply",
            "key": AutomicJobType.SupplyData,
            "lob": lob,
            "last_upload_date": res_dict.get(lob, [None])[0] and res_dict.get(lob, [None])[0].strftime(DateTimeFormat)
        })
    return ret


def get_esr_record_list(key, channel: str):

    table_name = KEY_TABLE_NAME_MAPPING.get(key)
    if key in [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value]:
        data_type = AutomaticDataType.ESR.value
    else:
        data_type = AutomaticDataType.CTO.value

    # res = AppFastESRMonWi.get_esr_record_list(fiscal_week_year)
    # 更换ESR record 列表查询方式
    # res = AppGbiTableSyncStatusDi.query_sync_status_records(data_type=AutomaticDataType.ESR.value,
    #                                                         db_name=Automatic_DB_NAME,
    #                                                         table_name=table_name)

    res = AutomaticFileInfo.query_sync_status_records(table_name=table_name,
                                                      data_type=data_type,
                                                      db_name=Automatic_DB_NAME,
                                                      rtm_view=channel)

    ret_list = [{
        "name": KEY_ESR_NAME_MAPPING.get(key),
        "last_upload_date": x.sync_time.strftime('%Y-%m-%d %H:%M:%S'),
        "last_upload_by": "System",
        "fiscal_qtr_week_name": x.fiscal_qtr_week_name,
        "file_path": x.file_path
    } for x in res]
    # 对ret_list按照fiscal_week_year进行分组
    ret_dict = {}
    for item in ret_list:
        if item['fiscal_qtr_week_name'] not in ret_dict:
            ret_dict[item['fiscal_qtr_week_name']] = []
        ret_dict[item['fiscal_qtr_week_name']].append(item)
    ret = []
    for key in ret_dict:
        ret.append({
            "fiscal_qtr_week_name": key,
            "data": ret_dict[key]
        })
    return ret


def get_record_list_by_cmd(lob):

    # res = AppFastESRMonWi.get_esr_record_list(fiscal_week_year)
    # 更换ESR record 列表查询方式
    res = DataSourceFileRecordRepository.query_record_by_cmd(AutomicJobType.SupplyData, lob)

    ret_list = [{
        "record_id": x.id,
        "last_upload_date": x.create_at and x.create_at.strftime(DateTimeFormat),
        # 系统操作人外显的时候统一显示
        "last_upload_by": x.upload_by if x.upload_by != SYSTEM_OPERATOR else "System",
        "file_name": x.file_name,
        "fiscal_qtr_week_name": x.fiscal_qtr_week_name
    } for x in res]
    # 对ret_list按照fiscal_qtr_week_name进行分组
    ret_dict = {}
    for item in ret_list:
        if item['fiscal_qtr_week_name'] not in ret_dict:
            ret_dict[item['fiscal_qtr_week_name']] = []
        ret_dict[item['fiscal_qtr_week_name']].append(item)
    ret = []
    # 对ret_dict按照fiscal_qtr_week_name进行分组
    for key in ret_dict:
        ret.append({
            "fiscal_qtr_week_name": key,
            "data": ret_dict[key]
        })
    # 对ret按照sort_fiscal_qtr_week进行排序
    ret.sort(key=sort_fiscal_qtr_week, reverse=True)
    return ret


def sort_fiscal_qtr_week(x: dict):
    # 对fiscal_qtr_week_name按照Q和W进行分割，然后按照年份、季度、周进行排序
    year, qtr_name = x['fiscal_qtr_week_name'].split('Q')
    qtr, week = qtr_name.split('W')
    return int(year[2:]) * 1000 + int(qtr) * 100 + int(week)


def download_esr_record_by_version(fiscal_week_year: int, version: int):
    if version == ESRMondayVersion:
        # 下载周一版本的内容
        return AppFastESRMonWi.download_esr_content_by_week(fiscal_week_year)
    elif version == ESRWednesdayVersion:
        # 下载周三版本的内容
        return AppFastESRWedWi.download_esr_content_by_week(fiscal_week_year)
    elif version == ESRTuesdayVersion:
        # 下载周二上午版本的内容
        return AppFastESRTuAMWi.download_esr_content_by_week(fiscal_week_year)
    else:
        return pd.DataFrame()


def file_save_cpf_twos_iphone(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    
    cpf_twos_iphone_validate_data(df)
    
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCPF
    file_info.datasource_type = DataSourceFileType.CPFTWOSiPhone
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCPF, DataSourceFileType.CPFTWOSiPhone)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCPF, DataSourceFileType.CPFTWOSiPhone, today)
    if today_count == 0:
        file_info.filename = f"TWOS_iPhone_{today}.xlsx"
    else:
        file_info.filename = f"TWOS_iPhone_{today}_{today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    cfp_save_data_source_twos_iphone(df, version, uploader, today)
    file_info.save()
    return {"file_id": file_md5}


def cpf_twos_iphone_validate_data(df: pd.DataFrame):
    # table header validate
    if not columns_equals(CPF_TWOS_IPHONE_TEMPLATE_HEADER, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFTWOSiPhoneHeaderError)
    
    # Model validate
    model_list = DimPubProdInfo.get_valid_model_list('iPhone')
    condition = ~df['Model'].isin(model_list)
    invalid_model_rows = df.loc[condition].index.tolist()
    if len(invalid_model_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_model_rows])
                          + FileUploadError.CPFTWOSiPhoneModelInvalidSuffix)
    
    # RTM/ Business Type validate
    valid_rtm_business_type = OdsFastCPFSoldToMappingIPhone.get_rtm_business_type()
    not_in_tuple = ~df[['RTM', 'Business Type']].apply(lambda x: tuple(x) in valid_rtm_business_type, axis=1)
    invalid_rtm_business_type_rows = df.loc[not_in_tuple].index.tolist()
    if len(invalid_rtm_business_type_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_rtm_business_type_rows])
                          + FileUploadError.CPFTWOSiPhoneRTMBusinessTypeInvalidSuffix)
    
    # TWOS 数值校验：数值允许最多一位小数，不能为负数，且不可以为空值
    invalid_twos = ~df['TWOS'].apply(value_validate)
    invalid_twos_rows = df.loc[invalid_twos].index.tolist()
    if len(invalid_twos_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_twos_rows])
                          + FileUploadError.CPFTWOSiPhoneTWOSValueInvalidSuffix)
    
    # Type 校验：只能为“UB”或“SO”，需要跟RTM对应
    # type_list = ['UB', 'SO']
    # invalid_type = ~df['Type'].isin(type_list)
    # rtm_type_tuple = [('Mono', 'SO'), ('Multi', 'UB'), ('Online', 'SO'), ('Carrier', 'UB'), ('ENT', 'UB'), ('EDU', 'UB')]
    # invalid_type = ~df[['RTM', 'Type']].apply(lambda x: tuple(x) in rtm_type_tuple, axis=1)
    # invalid_type_rows = df.loc[invalid_type].index.tolist()
    # if len(invalid_type_rows) != 0:
    #     raise ErrorExcept(ErrCode.FileUploadError, 
    #                       FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
    #                       + ", ".join([str(idx+2) for idx in invalid_type_rows])
    #                       + FileUploadError.CPFTWOSiPhoneTypeInvalidSuffix)

    # HR/LR 校验：只能为“HR”或“LR”
    hr_lr_list = ['HR', 'LR']
    invalid_hr_lr = ~df['HR/LR'].isin(hr_lr_list)
    invalid_rows_hr_lr = df.loc[invalid_hr_lr].index.tolist()
    if len(invalid_rows_hr_lr) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          "The following data row(s) are invalid for wrong HR/LR values: "
                          + ", ".join([str(idx+2) for idx in invalid_rows_hr_lr]))

    # 重复行
    # duplicated = df.duplicated(subset=['Model','RTM','Business Type','HR/LR'], keep=False)
    # if duplicated.any():
    #     raise ErrorExcept(ErrCode.FileUploadError,
    #                       FileUploadError.CPFTIdealDemandDuplicateInvalid)

    # 在sold-to id & mpn的颗粒度上，不允许出现重复的数据行
    is_duplicate = df.duplicated(subset=['Sold-to ID', 'MPN'], keep=False)
    duplicate_rows = df.loc[is_duplicate].index.tolist()
    if duplicate_rows:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          "The following data row(s) are invalid for duplicated values: "
                          + ", ".join([str(idx+2) for idx in duplicate_rows]))

    pool = ThreadPoolExecutor(max_workers=4)
    datasource_sold_to_pool = pool.submit(OdsFastCPFSoldToMappingIPhone.get_all_rtm_sold_to_id)
    lob_sub_lob_mpn_pool = pool.submit(OdsFastCPFActiveSKULob.list_mpn, Lob.IPHONE.value)
    valid_datasource_sold_to_id_list = datasource_sold_to_pool.result()
    lob_sub_lob_mpn_list = lob_sub_lob_mpn_pool.result()

    # 产品纬度列合法性校验, 如用户上传的 MPN不在SKU List范围内
    invalid_mpn = ~df[['MPN']].apply(lambda x: tuple(x) in lob_sub_lob_mpn_list, axis=1)
    invalid_mpn_rows = df.loc[invalid_mpn].index.tolist()
    if invalid_mpn_rows:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          "The following data row(s) are invalid for wrong MPN values: "
                          + ", ".join([str(idx+2) for idx in invalid_mpn_rows]))

    # “Sold-to ID”字段值应包括在当前时点下最新版的[Data Source] - [CP&F] - [Sold-to Mapping List - iPhone]内，
    # 且“Sold-to ID”与RTM列的匹配关系应保持一致
    df['Sold-to ID'] = df['Sold-to ID'].astype(str)
    invalid_rtm_soldto = ~df[['RTM', 'Sold-to ID']].apply(lambda x: tuple(x) in valid_datasource_sold_to_id_list, axis=1)
    invalid_rtm_soldto_rows = df.loc[invalid_rtm_soldto].index.tolist()
    if invalid_rtm_soldto_rows:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          "The following data row(s) are invalid for wrong reseller info or mapping relationships: "
                          + ", ".join([str(idx+2) for idx in invalid_rtm_soldto_rows]))


def value_validate(value):
    if isinstance(value, str):
        return False
    if pd.isna(value): # 如果为空值，返回 False
        return False
    elif value < 0: # 如果为负数，返回 False
        return False
    else:
        return round(value, 1) == value # 如果小数位数超过 1，返回 False；否则返回 True


def cfp_save_data_source_twos_iphone(df: pd.DataFrame, version: int, upload_by: str, upload_date: str):
    old_datas_count = OdsFastCPFTWOSiPhone.query_by_version(version)
    if old_datas_count != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    week_info = FiscalYearWeek.get_week_by_date(upload_date)
    if len(week_info) == 0:
        raise ErrorExcept(ErrCode.System, "get week info error.")
    
    datas = []
    # 上传的文件中可以有空值，需要转化为None才可在数据库中保存
    df.replace({np.nan: None}, inplace=True)
    for _, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": week_info['fiscal_qtr_week_name'],
            "fiscal_week_year": week_info['fiscal_week_year'],
            "upload_by": upload_by,
            "upload_at": datetime.now(),
            "model": row["Model"],
            "mpn": row["MPN"],
            "rtm": row["RTM"],
            "business_type": row["Business Type"],
            "sold_to_id": row["Sold-to ID"],
            "sold_to_name": row["Sold-to Name"],
            "twos": row["TWOS"],
            "hr_lr": row["HR/LR"],
            "version": version,
        })
    OdsFastCPFTWOSiPhone.bulk_save(datas)


def file_save_sold_to_mapping_iphone(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    cpf_sold_to_mapping_iphone_validate_data(df)

    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCPF
    file_info.datasource_type = DataSourceFileType.CPFSoldToMappingListIphone
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCPF, DataSourceFileType.CPFSoldToMappingListIphone)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCPF, DataSourceFileType.CPFSoldToMappingListIphone, today)
    if today_count == 0:
        file_info.filename = f"Sold-to Mapping List - iPhone_{today}.xlsx"
    else:
        file_info.filename = f"Sold-to Mapping List - iPhone_{today}_{today_count}.xlsx"
    file_info.status = DataSourceFileStatus.Enabled
    cfp_save_data_source_sold_to_mapping_iphone(df, version, uploader, today)
    file_info.save()
    return {"file_id": file_md5}


def cpf_sold_to_mapping_iphone_validate_data(df: pd.DataFrame):
    # table header validate
    if not columns_equals(CPF_SOLD_TO_MAPPING_LIST_TEMPLATE_HEADER, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFTSoldToMappingListHeaderError)
    
    def check_invalid_rows(df: pd.DataFrame, validate_filed: str, valid_list: list, error_suffix: str):
        invalid_condition = ~df[validate_filed].astype(str).isin(valid_list)
        invalid_rows = df.loc[invalid_condition].index.tolist()
        if len(invalid_rows) != 0:
            raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.CPFTSoldToMappingListRowsPrefix
                            + ", ".join([str(idx + 2) for idx in invalid_rows])
                            + error_suffix)
    
    # 合法性校验：正确的枚举值包括且仅包括“China Mainland”、“Hong Kong”或“Taiwan”
    # 不一致时报错，文案：“The followings row(s) are invalid: {xx}{xx}{xx} for wrong Region name.”
    region_list = ["China mainland", "Hong Kong", "Taiwan"]
    check_invalid_rows(df, "Region", region_list, " for wrong Region name.")

    # RTM validate
    rtm_list = ['Mono', 'Multi', 'Online', 'Carrier', 'Enterprise', 'Education', 'Retail Partner']
    check_invalid_rows(df, "RTM", rtm_list, FileUploadError.CPFTSoldToMappingListRowsRTMInvalidSuffix)
    
    valid_sold_to_list = AppFastIdealEsrSoldToMappingWi.query_all_sold_id()
    valid_sold_to_list.append("1118511")  # JD-Self Run
    check_invalid_rows(df, "Sold-to ID", valid_sold_to_list, " for non existent Sold-to ID")


def cfp_save_data_source_sold_to_mapping_iphone(df: pd.DataFrame, version: int, upload_by: str, upload_date: str):
    old_datas_count = OdsFastCPFSoldToMappingIPhone.query_by_version(version)
    if old_datas_count != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    week_info = FiscalYearWeek.get_week_by_date(upload_date)
    if len(week_info) == 0:
        raise ErrorExcept(ErrCode.System, "get week info error.")

    datas = []
    # 上传的文件中可以有空值，需要转化为None才可在数据库中保存
    df.replace({np.nan: None}, inplace=True)
    for _, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": week_info['fiscal_qtr_week_name'],
            "fiscal_week_year": week_info['fiscal_week_year'],
            "upload_by": upload_by,
            "upload_at": datetime.now(),
            "region": row["Region"],
            "rtm": row["RTM"],
            "business_type": row["Business Type"],
            "sold_to_id": row["Sold-to ID"],
            "sold_to_name":row.get("Name"),
            "sold_to_name_abbre": row["Abbre."],
            "remark": row["Remark"],
            "version": version,
        })
    OdsFastCPFSoldToMappingIPhone.bulk_save(datas)


def email_click_send(datasource_id, recipient):
    try:
        # 通过id获取对应的邮件
        datasource_automatic_update_record = DataSourceFileRecordRepository.query_record_by_id(datasource_id)
        params = ast.literal_eval(datasource_automatic_update_record.params)
        async_send_email_by_database(EmailCmd.SupplyDataRecordEmail,
                                     datasource_automatic_update_record.file_name,
                                     recipient,
                                     params)
    except Exception as e:
        print(e)
        params = {"error": f'datasource_id: {datasource_id}, recipient: {recipient}. error: {traceback.format_exc()}'}
        send_email_by_database(EmailCmd.WarningEmail, file_name='', recipients=None, params=params)
        raise ErrorExcept(ErrCode.System, "send email_report error")


def record_email_click_send(email, file_name):
    params = {
        'fiscal_qtr_week_name': 'FY24Q2W4',
        'lob': 'iPhone',
        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    async_send_email_by_database(EmailCmd.SupplyDataEmail, file_name,  email, params)


def file_save_datasource_sold_to_mix_ipad(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    # table header validate
    if not columns_equals(DataSourceIpadSoldToMixColumns, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.SpecialSupplyColumnsError)
    df.replace({np.nan: None}, inplace=True)
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCPF
    file_info.datasource_type = DataSourceFileType.AllocationRunSupplyMix
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCPF, DataSourceFileType.AllocationRunSupplyMix)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCPF, DataSourceFileType.AllocationRunSupplyMix, today)
    if today_count == 0:
        file_info.filename = f"{DataSourceFileType.AllocationRunSupplyMix}_{today}.xlsx"
    else:
        file_info.filename = f"{DataSourceFileType.AllocationRunSupplyMix}_{today} {today_count}.xlsx"
    week_info = FiscalYearWeek.get_week_by_date(today)
    if week_info is None:
        raise ErrorExcept(ErrCode.System, "get week info error.")
    file_info.status = DataSourceFileStatus.Enabled
    # file process
    old_datas = OdsFastCPFMixiPad.query_by_version(version)
    if old_datas != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    datas = []
    for index, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": week_info['fiscal_qtr_week_name'],
            "fiscal_week_year": week_info['fiscal_week_year'],
            "upload_by": uploader,
            "upload_at": datetime.now(),
            "sales_org": row["Sales Org"],
            "rtml4": row["RTML4"],
            "fph1": row["LOB / FPH L1"],
            "fph3": row["Prod / FPH L3"],
            "project_code": row["Project Code"],
            "nand": row["Nand"],
            "color": row["Color"],
            "mpn": row["MPN / Apple Part #"],
            "odq": row["ODQ"],
            "sold_to_id": row["Customer Sold-to ID"],
            "sold_to_name": row["Customer Sold-to Name"],
            "mix": row["Mix"],
            "version": version,
        })
    OdsFastCPFMixiPad.bulk_save(datas)
    file_info.save()
    return {"file_id": file_md5}


def file_save_datasource_twos_ipad(file, uploader):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    # table header validate
    if not columns_equals(DataSourceIpadTWOSColumns, df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.SpecialSupplyColumnsError)
    df.replace({np.nan: None}, inplace=True)
    # 文件信息保存
    file_info = DataSourceFile()
    file_info.rtm = StrRTMCPF
    file_info.datasource_type = DataSourceFileType.AllocationRunSupplyTWOS
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = datetime.now().strftime(DateFormat)
    file_info.upload_by = uploader
    file_info.status = DataSourceFileStatus.Draft
    file_info.url = f'/file/download/{file_md5}'
    version = DataSourceFile.query_count_by_rtm_and_type(StrRTMCPF, DataSourceFileType.AllocationRunSupplyTWOS)
    file_info.version = version
    today = datetime.now().strftime(DateFormat)
    today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMCPF, DataSourceFileType.AllocationRunSupplyTWOS, today)
    if today_count == 0:
        file_info.filename = f"{DataSourceFileType.AllocationRunSupplyTWOS}_{today}.xlsx"
    else:
        file_info.filename = f"{DataSourceFileType.AllocationRunSupplyTWOS}_{today} {today_count}.xlsx"
    week_info = FiscalYearWeek.get_week_by_date(today)
    if week_info is None:
        raise ErrorExcept(ErrCode.System, "get week info error.")
    file_info.status = DataSourceFileStatus.Enabled
    # file process
    old_datas = OdsFastCPFTWOSiPad.query_by_version(version)
    if old_datas != 0:
        raise ErrorExcept(ErrCode.DBInsert, "have same version data")
    datas = []
    for index, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": week_info['fiscal_qtr_week_name'],
            "fiscal_week_year": week_info['fiscal_week_year'],
            "upload_by": uploader,
            "upload_at": datetime.now(),
            "sales_org": row["Sales Org"],
            "rtml4": row["RTML4"],
            "fph1": row["LOB / FPH L1"],
            "fph3": row["Prod / FPH L3"],
            "project_code": row["Project Code"],
            "nand": row["Nand"],
            "color": row["Color"],
            "mpn": row["MPN / Apple Part #"],
            "odq": row["ODQ"],
            "sold_to_id": row["Customer Sold-to ID"],
            "sold_to_name": row["Customer Sold-to Name"],
            "twos": row["TWOS"],
            "type": row["Type"],
            "version": version,
        })
    OdsFastCPFTWOSiPad.bulk_save(datas)
    file_info.save()
    return {"file_id": file_md5}


@celery.task(name="download_esr_record_and_send_email")
def download_esr_record_and_send_email(fiscal_week_year: int, version: int, fiscal_qtr_week_name: str, receiver: str, record_time: str) -> None:

    try:
        esr_content = download_esr_record_by_version(fiscal_week_year, version)
        csv_name = f"ESR Data - [{fiscal_qtr_week_name}] - [{record_time}].csv"
        zip_name = f"ESR Data - [{fiscal_qtr_week_name}] - [{record_time}].zip"
        params = {
            "fiscal_week_year": fiscal_week_year,
            "fiscal_qtr_week_name": fiscal_qtr_week_name,
            'time': record_time
        }
        esr_content.to_csv(csv_name, index=False)
        zip_path = save_as_zip(csv_name, csv_name, '', zip_name)
        send_email_by_database(EmailCmd.ESRRecordEmail, zip_path, receiver, params)
    except Exception as e:
        print(e)
        params = {"error": f'fiscal_week_year: {fiscal_week_year}, version: {version}, recipient: {receiver}. error: {traceback.format_exc()}'}
        send_email_by_database(EmailCmd.WarningEmail, file_name='', recipients=None, params=params)
        raise ErrorExcept(ErrCode.System, "send email_report error")


def get_suspension_datasource_file_list(default_limit=13):
    res = DataSourceFile.query_suspension_upload_files(SUSPENSION_APPEAL_UPLOAD_TYPE)
    # 按照fiscal_week 分类
    grouped_data = []
    for key, group in groupby(res, key=lambda x: (x.upload_date)):
        group_list = [{
            "file_name": x.filename,
            "upload_by": x.upload_by,
            "fiscal_week_name": x.upload_date,
            "upload_date": x.create_date and x.create_date.strftime(DateTimeFormat),
            "file_path": x.url} for x in list(group)]
        grouped_data.append({
            'fiscal_week_name': key,
            'data': group_list
        })
    # 支持查看最新13周的上传记录
    return grouped_data[:default_limit]

